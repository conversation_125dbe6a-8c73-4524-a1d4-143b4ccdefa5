
import React from 'react';
import { ChevronRight, Home } from 'lucide-react';
import { generateBreadcrumbSchema } from '@/utils/seo';
import { Helmet } from 'react-helmet-async';

interface BreadcrumbItem {
  name: string;
  url: string;
}

interface SEOBreadcrumbProps {
  breadcrumbs: BreadcrumbItem[];
  className?: string;
}

export const SEOBreadcrumb: React.FC<SEOBreadcrumbProps> = ({ 
  breadcrumbs, 
  className = '' 
}) => {
  // Don't render if no breadcrumbs
  if (!breadcrumbs || breadcrumbs.length === 0) {
    return null;
  }

  const breadcrumbSchema = generateBreadcrumbSchema(breadcrumbs);

  return (
    <>
      <Helmet>
        <script type="application/ld+json">
          {JSON.stringify(breadcrumbSchema)}
        </script>
      </Helmet>
      
      <nav 
        className={`bg-muted/20 py-2 ${className}`}
        aria-label="Breadcrumb"
      >
        <div className="container mx-auto px-4">
          <ol className="flex items-center space-x-2 text-sm">
            {breadcrumbs.map((crumb, index) => (
              <li key={index} className="flex items-center">
                {index > 0 && (
                  <ChevronRight className="h-4 w-4 text-muted-foreground mx-2" />
                )}
                {index === 0 && (
                  <Home className="h-4 w-4 text-muted-foreground mr-1" />
                )}
                {index === breadcrumbs.length - 1 ? (
                  <span className="text-foreground font-medium">
                    {crumb.name}
                  </span>
                ) : (
                  <a 
                    href={crumb.url}
                    className="text-muted-foreground hover:text-foreground transition-colors"
                  >
                    {crumb.name}
                  </a>
                )}
              </li>
            ))}
          </ol>
        </div>
      </nav>
    </>
  );
};
