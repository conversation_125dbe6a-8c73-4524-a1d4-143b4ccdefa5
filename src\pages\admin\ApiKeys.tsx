import React from 'react';
import { Head<PERSON> } from '@/components/Header';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Key, Plus, Refresh<PERSON>w, <PERSON>, <PERSON>Off, Trash2 } from 'lucide-react';

const AdminApiKeys: React.FC = () => {
  const [showKeys, setShowKeys] = React.useState<{ [key: string]: boolean }>({});

  const apiKeys = [
    { id: '1', name: 'Production API', key: 'sk-prod-1234567890abcdef', created: '2024-01-15', lastUsed: '2024-01-27' },
    { id: '2', name: 'Development API', key: 'sk-dev-abcdef1234567890', created: '2024-01-10', lastUsed: '2024-01-26' },
    { id: '3', name: 'Testing API', key: 'sk-test-fedcba0987654321', created: '2024-01-20', lastUsed: '2024-01-25' }
  ];

  const toggleKeyVisibility = (id: string) => {
    setShowKeys(prev => ({
      ...prev,
      [id]: !prev[id]
    }));
  };

  const maskKey = (key: string) => {
    return key.substring(0, 12) + '••••••••••••••••';
  };

  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-foreground mb-2">API Key Management</h1>
          <p className="text-muted-foreground">Manage API keys for ML Sports Intelligence Engine</p>
        </div>

        <div className="grid gap-6">
          <Card className="border-border shadow-carved-deep bg-card">
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Key className="h-5 w-5" />
                  API Keys
                </div>
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  Generate New Key
                </Button>
              </CardTitle>
              <CardDescription>
                Manage API keys for accessing the ML Sports Intelligence Engine
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {apiKeys.map((apiKey) => (
                  <div key={apiKey.id} className="p-4 border border-border rounded-lg shadow-carved bg-card">
                    <div className="flex items-center justify-between">
                      <div className="space-y-2 flex-1">
                        <div className="flex items-center gap-2">
                          <h3 className="font-medium text-foreground">{apiKey.name}</h3>
                          <span className="px-2 py-1 text-xs bg-success/20 text-success rounded">Active</span>
                        </div>
                        <div className="font-mono text-sm text-muted-foreground">
                          {showKeys[apiKey.id] ? apiKey.key : maskKey(apiKey.key)}
                        </div>
                        <div className="text-xs text-muted-foreground">
                          Created: {apiKey.created} • Last used: {apiKey.lastUsed}
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => toggleKeyVisibility(apiKey.id)}
                        >
                          {showKeys[apiKey.id] ? (
                            <EyeOff className="h-4 w-4" />
                          ) : (
                            <Eye className="h-4 w-4" />
                          )}
                        </Button>
                        <Button variant="outline" size="sm">
                          <RefreshCw className="h-4 w-4" />
                        </Button>
                        <Button variant="destructive" size="sm">
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          <Card className="border-border shadow-carved-deep bg-card">
            <CardHeader>
              <CardTitle>API Usage Statistics</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="text-center p-4 border border-border rounded-lg shadow-carved bg-card">
                  <div className="text-2xl font-bold text-primary">1,247,832</div>
                  <div className="text-sm text-muted-foreground">Total Requests</div>
                </div>
                <div className="text-center p-4 border border-border rounded-lg shadow-carved bg-card">
                  <div className="text-2xl font-bold text-success">23,456</div>
                  <div className="text-sm text-muted-foreground">Today's Requests</div>
                </div>
                <div className="text-center p-4 border border-border rounded-lg shadow-carved bg-card">
                  <div className="text-2xl font-bold text-info">98.7%</div>
                  <div className="text-sm text-muted-foreground">Success Rate</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default AdminApiKeys;