name: Deploy Edge Functions

on:
  push:
    branches: [ main, develop ]
    paths:
      - 'supabase/functions/**'
  pull_request:
    branches: [ main ]
    paths:
      - 'supabase/functions/**'
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        default: 'production'
        type: choice
        options:
        - production
        - staging

jobs:
  test-functions:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Deno
        uses: denoland/setup-deno@v1
        with:
          deno-version: v1.37.x
          
      - name: Lint Edge Functions
        run: |
          cd supabase/functions
          for dir in */; do
            if [ -f "$dir/index.ts" ]; then
              echo "Linting $dir"
              deno lint "$dir/index.ts" || true
            fi
          done
          
      - name: Type Check Edge Functions
        run: |
          cd supabase/functions
          for dir in */; do
            if [ -f "$dir/index.ts" ]; then
              echo "Type checking $dir"
              deno check "$dir/index.ts" || true
            fi
          done

  deploy-staging:
    if: github.ref == 'refs/heads/develop' || (github.event_name == 'workflow_dispatch' && github.event.inputs.environment == 'staging')
    needs: test-functions
    runs-on: ubuntu-latest
    environment: staging
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Supabase CLI
        uses: supabase/setup-cli@v1
        with:
          version: latest
          
      - name: Deploy to Staging
        env:
          SUPABASE_ACCESS_TOKEN: ${{ secrets.SUPABASE_ACCESS_TOKEN }}
          SUPABASE_PROJECT_ID: ${{ secrets.SUPABASE_PROJECT_ID_STAGING }}
        run: |
          supabase functions deploy scrape-and-predict --project-ref $SUPABASE_PROJECT_ID
          supabase functions deploy monitoring-dashboard --project-ref $SUPABASE_PROJECT_ID
          
      - name: Set Staging Secrets
        env:
          SUPABASE_ACCESS_TOKEN: ${{ secrets.SUPABASE_ACCESS_TOKEN }}
          SUPABASE_PROJECT_ID: ${{ secrets.SUPABASE_PROJECT_ID_STAGING }}
        run: |
          supabase secrets set SUPABASE_URL="${{ secrets.SUPABASE_URL_STAGING }}" --project-ref $SUPABASE_PROJECT_ID
          supabase secrets set SUPABASE_SERVICE_ROLE_KEY="${{ secrets.SUPABASE_SERVICE_ROLE_KEY_STAGING }}" --project-ref $SUPABASE_PROJECT_ID
          supabase secrets set PY_SERVICE_URL="${{ secrets.PY_SERVICE_URL_STAGING }}" --project-ref $SUPABASE_PROJECT_ID

  deploy-production:
    if: github.ref == 'refs/heads/main' || (github.event_name == 'workflow_dispatch' && github.event.inputs.environment == 'production')
    needs: test-functions
    runs-on: ubuntu-latest
    environment: production
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Supabase CLI
        uses: supabase/setup-cli@v1
        with:
          version: latest
          
      - name: Deploy to Production
        env:
          SUPABASE_ACCESS_TOKEN: ${{ secrets.SUPABASE_ACCESS_TOKEN }}
          SUPABASE_PROJECT_ID: ${{ secrets.SUPABASE_PROJECT_ID }}
        run: |
          supabase functions deploy scrape-and-predict --project-ref $SUPABASE_PROJECT_ID
          supabase functions deploy monitoring-dashboard --project-ref $SUPABASE_PROJECT_ID
          
      - name: Set Production Secrets
        env:
          SUPABASE_ACCESS_TOKEN: ${{ secrets.SUPABASE_ACCESS_TOKEN }}
          SUPABASE_PROJECT_ID: ${{ secrets.SUPABASE_PROJECT_ID }}
        run: |
          supabase secrets set SUPABASE_URL="${{ secrets.SUPABASE_URL }}" --project-ref $SUPABASE_PROJECT_ID
          supabase secrets set SUPABASE_SERVICE_ROLE_KEY="${{ secrets.SUPABASE_SERVICE_ROLE_KEY }}" --project-ref $SUPABASE_PROJECT_ID
          supabase secrets set PY_SERVICE_URL="${{ secrets.PY_SERVICE_URL }}" --project-ref $SUPABASE_PROJECT_ID
          
      - name: Run Post-Deployment Tests
        env:
          SUPABASE_URL: ${{ secrets.SUPABASE_URL }}
          SUPABASE_ANON_KEY: ${{ secrets.SUPABASE_ANON_KEY }}
        run: |
          # Test function endpoints
          curl -f "$SUPABASE_URL/functions/v1/scrape-and-predict" \
            -H "Authorization: Bearer $SUPABASE_ANON_KEY" \
            -H "Content-Type: application/json" \
            -d '{"test": true}' || echo "Function test failed (expected without auth)"
            
          curl -f "$SUPABASE_URL/functions/v1/monitoring-dashboard" \
            -H "Authorization: Bearer $SUPABASE_ANON_KEY" || echo "Dashboard test failed (expected without auth)"
            
      - name: Notify Deployment Success
        if: success()
        run: |
          echo "✅ Edge functions deployed successfully to production!"
          echo "🎯 scrape-and-predict: ${{ secrets.SUPABASE_URL }}/functions/v1/scrape-and-predict"
          echo "📊 monitoring-dashboard: ${{ secrets.SUPABASE_URL }}/functions/v1/monitoring-dashboard"
