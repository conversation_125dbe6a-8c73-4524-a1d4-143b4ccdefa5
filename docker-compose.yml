version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: sports_prediction_db
    environment:
      POSTGRES_DB: sports_prediction_db
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    networks:
      - sports_prediction_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis Cache (Optional)
  redis:
    image: redis:7-alpine
    container_name: sports_prediction_redis
    ports:
      - "6379:6379"
    networks:
      - sports_prediction_network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Backend API
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: sports_prediction_backend
    environment:
      NODE_ENV: production
      DATABASE_URL: ********************************************/sports_prediction_db
      JWT_SECRET: your-super-secret-jwt-key-change-this-in-production
      REDIS_URL: redis://redis:6379
      PORT: 3001
    ports:
      - "3001:3001"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - sports_prediction_network
    volumes:
      - backend_logs:/app/logs
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped

  # Frontend
  frontend:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: sports_prediction_frontend
    environment:
      VITE_API_BASE_URL: http://localhost:3001/api
    ports:
      - "80:80"
    depends_on:
      backend:
        condition: service_healthy
    networks:
      - sports_prediction_network
    restart: unless-stopped

volumes:
  postgres_data:
  backend_logs:

networks:
  sports_prediction_network:
    driver: bridge
