import { useState, useEffect } from "react";
import { Head<PERSON> } from "@/components/Header";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from "@/components/ui/breadcrumb";
import { SportTypeSelector } from "@/components/SportTypeSelector";
import { LeagueSelector } from "@/components/LeagueSelector";
import { CategorySelector } from "@/components/CategorySelector";
import { apiService } from "@/services/api";
import { Search, Filter, Clock, Star, TrendingUp, Target } from "lucide-react";
import { Link } from "react-router-dom";

// This will be replaced with real tips from the database
const getTipsFromPredictions = async () => {
  try {
    const { data, error } = await apiService.getTodaysPredictions({ limit: 20 });
    if (error || !data) throw new Error('Failed to fetch tips');
    
    return data.map(prediction => ({
      id: prediction.id,
      title: `${prediction.home_team} vs ${prediction.away_team} - ${prediction.market}`,
      description: `AI-powered prediction with ${prediction.confidence}% confidence. Expected value: ${prediction.expected_value || 0}%`,
      sport: prediction.sport.toLowerCase(),
      league: prediction.league,
      market: prediction.market,
      pick: prediction.pick,
      confidence: prediction.confidence,
      odds: prediction.odds,
      edge: prediction.edge,
      value: prediction.expected_value || 0,
      tags: ['AI', prediction.source.toLowerCase()],
      analysis: `Statistical analysis shows strong value in this ${prediction.market} market. Our AI model has identified key patterns that suggest this outcome is more likely than the bookmaker odds indicate.`,
      timeframe: 'Today',
      status: 'active' as const,
      bookmaker: 'Multiple',
      lastUpdated: prediction.created_at
    }));
  } catch (error) {
    console.error('Error fetching tips:', error);
    return [];
  }
};

const Tips = () => {
  const [tips, setTips] = useState<any[]>([]);
  const [selectedSport, setSelectedSport] = useState<string>('all');
  const [selectedLeague, setSelectedLeague] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');
  const [searchQuery, setSearchQuery] = useState('');
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const loadTips = async () => {
      setIsLoading(true);
      const tipsData = await getTipsFromPredictions();
      setTips(tipsData);
      setIsLoading(false);
    };
    
    loadTips();
  }, []);

  const filteredTips = tips.filter(tip => 
    selectedSport === 'all' || tip.sport === selectedSport
  );

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 80) return 'text-confidence-high';
    if (confidence >= 65) return 'text-confidence-medium';
    return 'text-confidence-low';
  };

  const getConfidenceBadge = (confidence: number) => {
    if (confidence >= 80) return <Badge className="bg-confidence-high text-white">High</Badge>;
    if (confidence >= 65) return <Badge className="bg-confidence-medium text-white">Medium</Badge>;
    return <Badge className="bg-confidence-low text-white">Low</Badge>;
  };

  return (
    <div className="min-h-screen bg-gradient-hero">
      <Header />
      
      <main className="container mx-auto px-4 py-8">
        {/* Breadcrumb */}
        <Breadcrumb className="mb-6">
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink asChild>
                <Link to="/">Home</Link>
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage>Tips</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>

        {/* Hero Section */}
        <section className="mb-8">
          <Card className="bg-gradient-card shadow-glow relative overflow-hidden">
            {/* Background Chart Stats */}
            <div className="absolute inset-0 opacity-10">
              <svg width="100%" height="100%" viewBox="0 0 800 250" className="absolute inset-0">
                <defs>
                  <linearGradient id="tipsChartGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" stopColor="hsl(var(--primary))" />
                    <stop offset="100%" stopColor="hsl(var(--accent))" />
                  </linearGradient>
                </defs>
                {/* Chart bars */}
                <rect x="50" y="180" width="20" height="70" fill="url(#tipsChartGradient)" opacity="0.7" />
                <rect x="100" y="150" width="20" height="100" fill="url(#tipsChartGradient)" opacity="0.7" />
                <rect x="150" y="120" width="20" height="130" fill="url(#tipsChartGradient)" opacity="0.7" />
                <rect x="200" y="100" width="20" height="150" fill="url(#tipsChartGradient)" opacity="0.7" />
                <rect x="250" y="80" width="20" height="170" fill="url(#tipsChartGradient)" opacity="0.7" />
                <rect x="300" y="90" width="20" height="160" fill="url(#tipsChartGradient)" opacity="0.7" />
                <rect x="350" y="110" width="20" height="140" fill="url(#tipsChartGradient)" opacity="0.7" />
                {/* Data points */}
                <circle cx="160" cy="100" r="2" fill="hsl(var(--primary))" />
                <circle cx="210" cy="80" r="2" fill="hsl(var(--primary))" />
                <circle cx="260" cy="60" r="2" fill="hsl(var(--primary))" />
                <circle cx="310" cy="70" r="2" fill="hsl(var(--primary))" />
              </svg>
            </div>
            
            <div className="p-6 md:p-8 relative z-10">
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 items-center">
                {/* Text Content */}
                <div className="lg:col-span-2">
                  <h1 className="text-3xl md:text-5xl font-bebas tracking-wide text-foreground mb-4">
                    EXPERT BETTING
                    <br />
                    <span className="text-primary">TIPS</span>
                  </h1>
                  <p className="text-muted-foreground text-lg mb-4">
                    Professional betting insights powered by 1300BLK AI. Get detailed analysis, 
                    statistical backing, and high-value opportunities.
                  </p>
                </div>
                
                {/* Hero Image */}
                <div className="flex justify-center lg:justify-end">
                  <div className="relative">
                    <img 
                      src="/lovable-uploads/74f9ebe9-3442-443d-ad3c-e5046e67a481.png" 
                      alt="ML Sports Elite Athlete" 
                      className="w-full max-w-xs md:max-w-sm h-auto object-contain"
                    />
                    {/* Glowing effect behind image */}
                    <div className="absolute inset-0 bg-gradient-primary opacity-20 blur-3xl scale-110 -z-10"></div>
                  </div>
                </div>
              </div>
            </div>
          </Card>
        </section>

        {/* Filters Section */}
        <Card className="mb-8 bg-gradient-card">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 font-bebas tracking-wide">
              <Filter className="h-5 w-5" />
              Filter Tips
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Search */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search tips..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>

            {/* Filter Controls */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="text-sm font-medium mb-2 block">Sport</label>
                <SportTypeSelector
                  selectedSport={selectedSport}
                  onSportChange={setSelectedSport}
                />
              </div>
              <div>
                <label className="text-sm font-medium mb-2 block">League</label>
                <LeagueSelector
                  selectedSport={selectedSport}
                  selectedLeague={selectedLeague}
                  onLeagueChange={setSelectedLeague}
                />
              </div>
              <div>
                <label className="text-sm font-medium mb-2 block">Category</label>
                <CategorySelector
                  selectedCategory={selectedCategory}
                  onCategoryChange={setSelectedCategory}
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Tips Grid */}
        {isLoading ? (
          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6 mb-8">
            {[1, 2, 3, 4, 5, 6].map((i) => (
              <Card key={i} className="bg-gradient-card animate-pulse">
                <CardHeader>
                  <div className="h-6 bg-muted rounded mb-2"></div>
                  <div className="h-4 bg-muted rounded w-3/4"></div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="h-4 bg-muted rounded"></div>
                    <div className="h-4 bg-muted rounded w-2/3"></div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : filteredTips.length > 0 ? (
          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6 mb-8">
             {filteredTips.map((tip) => (
            <Card key={tip.id} className="bg-gradient-card hover:shadow-glow transition-all duration-300">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <CardTitle className="text-lg font-bebas tracking-wide mb-2">
                      {tip.title}
                    </CardTitle>
                    <div className="flex items-center gap-2 mb-2">
                      <Badge variant="secondary" className="text-xs">
                        {tip.sport}
                      </Badge>
                      <Badge variant="outline" className="text-xs">
                        {tip.league}
                      </Badge>
                    </div>
                  </div>
                  {getConfidenceBadge(tip.confidence)}
                </div>
              </CardHeader>
              <CardContent>
                <CardDescription className="mb-4 text-sm">
                  {tip.description}
                </CardDescription>

                {/* Stats */}
                <div className="grid grid-cols-3 gap-4 mb-4">
                  <div className="text-center">
                    <div className="text-lg font-bold text-primary">{tip.confidence}%</div>
                    <div className="text-xs text-muted-foreground">Confidence</div>
                  </div>
                  <div className="text-center">
                    <div className="text-lg font-bold text-success">{tip.odds}</div>
                    <div className="text-xs text-muted-foreground">Odds</div>
                  </div>
                  <div className="text-center">
                    <div className="text-lg font-bold text-warning">{tip.edge}%</div>
                    <div className="text-xs text-muted-foreground">Edge</div>
                  </div>
                </div>

                {/* Tags */}
                <div className="flex flex-wrap gap-1 mb-4">
                  {tip.tags.map((tag: string, index: number) => (
                    <Badge key={index} variant="outline" className="text-xs">
                      {tag}
                    </Badge>
                  ))}
                </div>

                {/* Footer */}
                <div className="flex items-center justify-between text-xs text-muted-foreground">
                  <div className="flex items-center gap-1">
                    <Clock className="h-3 w-3" />
                    Today
                  </div>
                  <div className="flex items-center gap-1">
                    <Star className="h-3 w-3" />
                    1300BLK AI
                  </div>
                </div>
              </CardContent>
            </Card>
            ))}
          </div>
        ) : (
          <Card className="text-center py-12">
            <CardContent>
              <Target className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
              <h3 className="text-lg font-semibold mb-2">No Tips Available</h3>
              <p className="text-muted-foreground">No tips found for the selected filters. Try adjusting your search criteria.</p>
            </CardContent>
          </Card>
        )}

        {/* Load More */}
        <div className="text-center">
          <Button variant="outline" size="lg">
            Load More Tips
          </Button>
        </div>

        {/* About Section */}
        <Card className="mt-12 bg-gradient-card">
          <CardHeader>
            <CardTitle className="font-bebas tracking-wide text-center">About Our Tips</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-center">
              <div>
                <Target className="h-8 w-8 mx-auto mb-3 text-primary" />
                <h3 className="font-semibold mb-2">AI-Powered Analysis</h3>
                <p className="text-sm text-muted-foreground">
                  Advanced machine learning algorithms analyze thousands of data points
                </p>
              </div>
              <div>
                <TrendingUp className="h-8 w-8 mx-auto mb-3 text-success" />
                <h3 className="font-semibold mb-2">Statistical Edge</h3>
                <p className="text-sm text-muted-foreground">
                  Every tip comes with detailed statistical backing and edge calculation
                </p>
              </div>
              <div>
                <Star className="h-8 w-8 mx-auto mb-3 text-warning" />
                <h3 className="font-semibold mb-2">Expert Validation</h3>
                <p className="text-sm text-muted-foreground">
                  All tips are validated by our team of professional analysts
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </main>
    </div>
  );
};

export default Tips;