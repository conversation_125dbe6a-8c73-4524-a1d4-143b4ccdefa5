import React, { useState, useEffect } from 'react';
import { Progress } from '@/components/ui/progress';

interface PreloaderStep {
  id: string;
  label: string;
  duration: number;
  icon?: React.ReactNode;
}

interface EnhancedPreloaderProps {
  onComplete: () => void;
  steps?: PreloaderStep[];
  showProgress?: boolean;
  className?: string;
}

const defaultSteps: PreloaderStep[] = [
  { id: 'auth', label: 'Authenticating User', duration: 800 },
  { id: 'data', label: 'Loading Predictions Data', duration: 1200 },
  { id: 'ui', label: 'Initializing Interface', duration: 600 },
  { id: 'ai', label: 'Connecting AI Engine', duration: 1000 },
  { id: 'ready', label: 'System Ready', duration: 400 }
];

export const EnhancedPreloader: React.FC<EnhancedPreloaderProps> = ({
  onComplete,
  steps = defaultSteps,
  showProgress = true,
  className = ''
}) => {
  const [currentStepIndex, setCurrentStepIndex] = useState(0);
  const [progress, setProgress] = useState(0);
  const [isVisible, setIsVisible] = useState(true);

  const currentStep = steps[currentStepIndex];
  const totalDuration = steps.reduce((sum, step) => sum + step.duration, 0);

  useEffect(() => {
    if (currentStepIndex >= steps.length) {
      // All steps completed
      setTimeout(() => {
        setIsVisible(false);
        setTimeout(onComplete, 300); // Allow fade out
      }, 200);
      return;
    }

    const stepDuration = currentStep.duration;
    const stepStartTime = Date.now();
    const previousProgress = steps
      .slice(0, currentStepIndex)
      .reduce((sum, step) => sum + step.duration, 0);

    const interval = setInterval(() => {
      const elapsed = Date.now() - stepStartTime;
      const stepProgress = Math.min(elapsed / stepDuration, 1);
      const totalProgress = ((previousProgress + (stepProgress * stepDuration)) / totalDuration) * 100;
      
      setProgress(totalProgress);

      if (stepProgress >= 1) {
        clearInterval(interval);
        setCurrentStepIndex(prev => prev + 1);
      }
    }, 16); // ~60fps

    return () => clearInterval(interval);
  }, [currentStepIndex, currentStep, steps, totalDuration, onComplete]);

  if (!isVisible) return null;

  return (
    <div className={`fixed inset-0 bg-background z-50 flex items-center justify-center transition-opacity duration-300 ${!isVisible ? 'opacity-0' : 'opacity-100'} ${className}`}>
      <div className="text-center px-4 max-w-md w-full">
        {/* Logo - Responsive sizing */}
        <div className="w-16 h-16 sm:w-20 sm:h-20 md:w-24 md:h-24 lg:w-28 lg:h-28 mx-auto mb-4 sm:mb-6">
          <img 
            src="/lovable-uploads/d2e31152-4343-4f36-a76d-fd6714c7dc7b.png" 
            alt="1300BLK AI" 
            className="w-full h-full object-contain drop-shadow-glow animate-pulse"
          />
        </div>
        
        {/* Brand Text */}
        <h1 className="text-xl sm:text-2xl md:text-3xl lg:text-4xl font-bruno tracking-wide text-foreground mb-6 sm:mb-8 text-shadow-glow">
          1300BLK AI
        </h1>
        
        {/* Progress Bar */}
        {showProgress && (
          <div className="space-y-3 sm:space-y-4 mb-6">
            <Progress 
              value={progress} 
              className="w-full h-2 sm:h-3"
            />
            <div className="text-xs sm:text-sm text-muted-foreground">
              {Math.round(progress)}% Complete
            </div>
          </div>
        )}
        
        {/* Current Step */}
        <div className="space-y-2">
          <div className="flex items-center justify-center space-x-2">
            {currentStep?.icon}
            <span className="text-sm sm:text-base md:text-lg font-medium text-foreground">
              {currentStep?.label}
            </span>
          </div>
          <div className="flex justify-center space-x-1">
            <div className="w-2 h-2 bg-primary rounded-full animate-bounce" style={{ animationDelay: '0ms' }} />
            <div className="w-2 h-2 bg-primary rounded-full animate-bounce" style={{ animationDelay: '150ms' }} />
            <div className="w-2 h-2 bg-primary rounded-full animate-bounce" style={{ animationDelay: '300ms' }} />
          </div>
        </div>
        
        {/* Device info for debugging - Remove in production */}
        <div className="mt-6 text-xs text-muted-foreground/30 font-mono">
          <span className="sm:hidden">Mobile</span>
          <span className="hidden sm:block md:hidden">SM</span>
          <span className="hidden md:block lg:hidden">MD</span>
          <span className="hidden lg:block xl:hidden">LG</span>
          <span className="hidden xl:block 2xl:hidden">XL</span>
          <span className="hidden 2xl:block">2XL</span>
        </div>
      </div>
    </div>
  );
};