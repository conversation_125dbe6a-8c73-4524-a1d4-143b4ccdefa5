import React, { useEffect, useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { Header } from '@/components/Header';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ScraperTestDashboard } from '@/components/ScraperTestDashboard';
import AutomationManager from '@/components/admin/AutomationManager';
import { apiService as api } from '@/services/api';
import { useToast } from '@/hooks/use-toast';
import {
  Users,
  TrendingUp,
  Brain,
  Settings,
  Shield,
  DollarSign,
  FileText,
  Target,
  BarChart3,
  Database,
  Key,
  Server,
  Zap,
  UserPlus,
  RefreshCw
} from 'lucide-react';

interface AdminStats {
  totalUsers: number;
  activeUsers: number;
  premiumUsers: number;
  recentUsers: number;
  totalPredictions: number;
  publishedPredictions: number;
  premiumPredictions: number;
  winRate: number;
  totalMatches: number;
  upcomingMatches: number;
  liveMatches: number;
  completedMatches: number;
}

const AdminDashboard: React.FC = () => {
  const location = useLocation();
  const { toast } = useToast();
  const [stats, setStats] = useState<AdminStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [adminCreated, setAdminCreated] = useState(false);
  const [isCreatingAdmin, setIsCreatingAdmin] = useState(false);

  // Check if current route matches admin page
  const isActiveRoute = (path: string) => location.pathname === path;

  const fetchStats = async () => {
    try {
      const [userStatsRes, predictionStatsRes, sportsStatsRes] = await Promise.all([
        api.getUserStats?.() || Promise.resolve({ success: false }),
        api.getPredictionStats(),
        api.getSportsDataStats()
      ]);

      if (userStatsRes.success && predictionStatsRes.success && sportsStatsRes.success) {
        setStats({
          totalUsers: userStatsRes.data?.totalUsers || 0,
          activeUsers: userStatsRes.data?.activeUsers || 0,
          premiumUsers: userStatsRes.data?.premiumUsers || 0,
          recentUsers: userStatsRes.data?.recentUsers || 0,
          totalPredictions: predictionStatsRes.data?.totalPredictions || 0,
          publishedPredictions: predictionStatsRes.data?.publishedPredictions || 0,
          premiumPredictions: predictionStatsRes.data?.premiumPredictions || 0,
          winRate: predictionStatsRes.data?.winRate || 0,
          totalMatches: sportsStatsRes.data?.totalMatches || 0,
          upcomingMatches: sportsStatsRes.data?.upcomingMatches || 0,
          liveMatches: sportsStatsRes.data?.liveMatches || 0,
          completedMatches: sportsStatsRes.data?.completedMatches || 0,
        });
      } else {
        throw new Error('Failed to fetch stats');
      }
    } catch (error) {
      console.error('Error fetching admin stats:', error);
      toast({
        title: "Error",
        description: "Failed to load dashboard statistics",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchStats();
  };

  const handleCreateAdminUser = async () => {
    setIsCreatingAdmin(true);
    try {
      const response = await api.register({
        email: '<EMAIL>',
        password: 'admin123',
        fullName: 'System Administrator',
        role: 'ADMIN'
      });

      if (response.success) {
        setAdminCreated(true);
        toast({
          title: "Success",
          description: "Admin user created successfully",
        });
      } else {
        throw new Error(response.error || 'Failed to create admin user');
      }
    } catch (error) {
      console.error('Error creating admin user:', error);
      toast({
        title: "Error",
        description: "Failed to create admin user",
        variant: "destructive",
      });
    } finally {
      setIsCreatingAdmin(false);
    }
  };

  useEffect(() => {
    fetchStats();
  }, []);

  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8 flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-foreground mb-2">Admin Dashboard</h1>
            <p className="text-muted-foreground">Manage and monitor your ML Sports Intelligence platform</p>
          </div>
          <Button
            onClick={handleRefresh}
            disabled={refreshing}
            variant="outline"
            size="sm"
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>

        {loading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {[...Array(8)].map((_, i) => (
              <Card key={i} className="border-border shadow-carved-deep bg-card">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <div className="h-4 bg-muted animate-pulse rounded w-24"></div>
                  <div className="h-4 w-4 bg-muted animate-pulse rounded"></div>
                </CardHeader>
                <CardContent>
                  <div className="h-8 bg-muted animate-pulse rounded w-16 mb-2"></div>
                  <div className="h-3 bg-muted animate-pulse rounded w-20"></div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : stats ? (
          <>
            {/* User Stats */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
              <Card className="border-border shadow-carved-deep bg-card">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Total Users</CardTitle>
                  <Users className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-primary">{stats.totalUsers.toLocaleString()}</div>
                  <p className="text-xs text-muted-foreground">{stats.recentUsers} new this week</p>
                </CardContent>
              </Card>

              <Card className="border-border shadow-carved-deep bg-card">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Active Users</CardTitle>
                  <TrendingUp className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-primary">{stats.activeUsers.toLocaleString()}</div>
                  <p className="text-xs text-muted-foreground">Last 30 days</p>
                </CardContent>
              </Card>

              <Card className="border-border shadow-carved-deep bg-card">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Premium Users</CardTitle>
                  <DollarSign className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-primary">{stats.premiumUsers.toLocaleString()}</div>
                  <p className="text-xs text-muted-foreground">{((stats.premiumUsers / stats.totalUsers) * 100).toFixed(1)}% conversion</p>
                </CardContent>
              </Card>

              <Card className="border-border shadow-carved-deep bg-card">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Prediction Accuracy</CardTitle>
                  <Target className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-primary">{stats.winRate.toFixed(1)}%</div>
                  <p className="text-xs text-muted-foreground">Overall win rate</p>
                </CardContent>
              </Card>
            </div>

            {/* Prediction & Sports Data Stats */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
              <Card className="border-border shadow-carved-deep bg-card">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Total Predictions</CardTitle>
                  <Brain className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-primary">{stats.totalPredictions.toLocaleString()}</div>
                  <p className="text-xs text-muted-foreground">{stats.publishedPredictions} published</p>
                </CardContent>
              </Card>

              <Card className="border-border shadow-carved-deep bg-card">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Premium Predictions</CardTitle>
                  <Shield className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-primary">{stats.premiumPredictions.toLocaleString()}</div>
                  <p className="text-xs text-muted-foreground">High confidence picks</p>
                </CardContent>
              </Card>

              <Card className="border-border shadow-carved-deep bg-card">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Live Matches</CardTitle>
                  <Zap className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-primary">{stats.liveMatches}</div>
                  <p className="text-xs text-muted-foreground">{stats.upcomingMatches} upcoming</p>
                </CardContent>
              </Card>

              <Card className="border-border shadow-carved-deep bg-card">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Total Matches</CardTitle>
                  <Database className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-primary">{stats.totalMatches.toLocaleString()}</div>
                  <p className="text-xs text-muted-foreground">{stats.completedMatches} completed</p>
                </CardContent>
              </Card>
            </div>
          </>
        ) : (
          <div className="text-center py-8">
            <p className="text-muted-foreground">Failed to load dashboard statistics</p>
            <Button onClick={handleRefresh} className="mt-4">
              Try Again
            </Button>
          </div>
        )}

        {/* Data Scraping Dashboard */}
        <div className="mb-8">
          <h2 className="text-2xl font-bold text-foreground mb-4">Data Scraping & Prediction System</h2>
          <ScraperTestDashboard />
        </div>

        {/* Management Sections */}
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
          {/* User Management */}
          <Card className="border-border shadow-carved-deep bg-card">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                User Management
              </CardTitle>
              <CardDescription>Manage users, subscriptions, and permissions</CardDescription>
            </CardHeader>
            <CardContent className="space-y-2">
              <Button 
                variant={isActiveRoute('/admin/users') ? 'default' : 'outline'} 
                className="w-full justify-start" 
                asChild
              >
                <Link to="/admin/users">
                  <Users className="h-4 w-4 mr-2" />
                  User Accounts
                </Link>
              </Button>
              <Button 
                variant={isActiveRoute('/admin/subscriptions') ? 'default' : 'outline'} 
                className="w-full justify-start" 
                asChild
              >
                <Link to="/admin/subscriptions">
                  <DollarSign className="h-4 w-4 mr-2" />
                  Subscriptions
                </Link>
              </Button>
              <Button 
                variant={adminCreated ? 'secondary' : 'outline'} 
                className="w-full justify-start" 
                onClick={handleCreateAdminUser}
                disabled={isCreatingAdmin || adminCreated}
              >
                <UserPlus className="h-4 w-4 mr-2" />
                {adminCreated ? 'Admin User Created ✓' : isCreatingAdmin ? 'Creating Admin...' : 'Create Admin User'}
              </Button>
            </CardContent>
          </Card>

          {/* Content Management */}
          <Card className="border-border shadow-carved-deep bg-card">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Content Management
              </CardTitle>
              <CardDescription>Manage predictions, leagues, and content</CardDescription>
            </CardHeader>
            <CardContent className="space-y-2">
              <Button 
                variant={isActiveRoute('/admin/predictions') ? 'default' : 'outline'} 
                className="w-full justify-start" 
                asChild
              >
                <Link to="/admin/predictions">
                  <Target className="h-4 w-4 mr-2" />
                  Predictions
                </Link>
              </Button>
              <Button 
                variant={isActiveRoute('/admin/leagues') ? 'default' : 'outline'} 
                className="w-full justify-start" 
                asChild
              >
                <Link to="/admin/leagues">
                  <TrendingUp className="h-4 w-4 mr-2" />
                  Leagues
                </Link>
              </Button>
            </CardContent>
          </Card>

          {/* AI & ML Models */}
          <Card className="border-border shadow-carved-deep bg-card">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Brain className="h-5 w-5" />
                AI & ML Models
              </CardTitle>
              <CardDescription>Configure and monitor AI models</CardDescription>
            </CardHeader>
            <CardContent className="space-y-2">
              <Button 
                variant={isActiveRoute('/admin/models') ? 'default' : 'outline'} 
                className="w-full justify-start" 
                asChild
              >
                <Link to="/admin/models">
                  <Brain className="h-4 w-4 mr-2" />
                  Model Training
                </Link>
              </Button>
              <Button 
                variant={isActiveRoute('/admin/api-keys') ? 'default' : 'outline'} 
                className="w-full justify-start" 
                asChild
              >
                <Link to="/admin/api-keys">
                  <Key className="h-4 w-4 mr-2" />
                  API Keys
                </Link>
              </Button>
            </CardContent>
          </Card>

          {/* System Management */}
          <Card className="border-border shadow-carved-deep bg-card">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Server className="h-5 w-5" />
                System Management
              </CardTitle>
              <CardDescription>System monitoring and configuration</CardDescription>
            </CardHeader>
            <CardContent className="space-y-2">
              <Button 
                variant={isActiveRoute('/admin/analytics') ? 'default' : 'outline'} 
                className="w-full justify-start" 
                asChild
              >
                <Link to="/admin/analytics">
                  <BarChart3 className="h-4 w-4 mr-2" />
                  Analytics
                </Link>
              </Button>
              <Button 
                variant={isActiveRoute('/admin/database') ? 'default' : 'outline'} 
                className="w-full justify-start" 
                asChild
              >
                <Link to="/admin/database">
                  <Database className="h-4 w-4 mr-2" />
                  Database
                </Link>
              </Button>
            </CardContent>
          </Card>

          {/* Security & Settings */}
          <Card className="border-border shadow-carved-deep bg-card">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5" />
                Security & Settings
              </CardTitle>
              <CardDescription>Security configuration and system settings</CardDescription>
            </CardHeader>
            <CardContent className="space-y-2">
              <Button 
                variant={isActiveRoute('/admin/settings') ? 'default' : 'outline'} 
                className="w-full justify-start" 
                asChild
              >
                <Link to="/admin/settings">
                  <Settings className="h-4 w-4 mr-2" />
                  System Settings
                </Link>
              </Button>
              <Button 
                variant={isActiveRoute('/admin/security') ? 'default' : 'outline'} 
                className="w-full justify-start" 
                asChild
              >
                <Link to="/admin/security">
                  <Shield className="h-4 w-4 mr-2" />
                  Security
                </Link>
              </Button>
            </CardContent>
          </Card>

          {/* Data Scraping & ML Pipeline */}
          <Card className="border-border shadow-carved-deep bg-card">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Zap className="h-5 w-5" />
                Data Scraping & ML Pipeline
              </CardTitle>
              <CardDescription>Test and manage data scraping and prediction generation</CardDescription>
            </CardHeader>
            <CardContent className="space-y-2">
              <Button 
                variant={isActiveRoute('/admin/scraper') ? 'default' : 'outline'} 
                className="w-full justify-start" 
                asChild
              >
                <Link to="/admin/scraper">
                  <Zap className="h-4 w-4 mr-2" />
                  Scraper Dashboard
                </Link>
              </Button>
              <Button 
                variant={isActiveRoute('/admin/data-sources') ? 'default' : 'outline'} 
                className="w-full justify-start" 
                asChild
              >
                <Link to="/admin/data-sources">
                  <Database className="h-4 w-4 mr-2" />
                  Data Sources
                </Link>
              </Button>
            </CardContent>
          </Card>

          {/* Automation System */}
          <Card className="border-border shadow-carved-deep bg-card">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                Automation System
              </CardTitle>
              <CardDescription>Automated scraping and prediction generation</CardDescription>
            </CardHeader>
            <CardContent>
              <AutomationManager />
            </CardContent>
          </Card>

          {/* AdSense Management */}
          <Card className="border-border shadow-carved-deep bg-card">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <DollarSign className="h-5 w-5" />
                AdSense Management
              </CardTitle>
              <CardDescription>Manage advertising and revenue</CardDescription>
            </CardHeader>
            <CardContent className="space-y-2">
              <Button 
                variant={isActiveRoute('/admin/ads') ? 'default' : 'outline'} 
                className="w-full justify-start" 
                asChild
              >
                <Link to="/admin/ads">
                  <DollarSign className="h-4 w-4 mr-2" />
                  Ad Management
                </Link>
              </Button>
              <Button 
                variant={isActiveRoute('/admin/revenue') ? 'default' : 'outline'} 
                className="w-full justify-start" 
                asChild
              >
                <Link to="/admin/revenue">
                  <BarChart3 className="h-4 w-4 mr-2" />
                  Revenue Reports
                </Link>
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default AdminDashboard;