
import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Bitcoin, Wallet, CreditCard, Loader2 } from 'lucide-react';
import { useCryptoPayments } from '@/hooks/useCryptoPayments';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from 'sonner';

interface CryptoPaymentOptionsProps {
  plan: {
    name: string;
    price: number;
    tier: string;
  };
}

export const CryptoPaymentOptions: React.FC<CryptoPaymentOptionsProps> = ({ plan }) => {
  const { user } = useAuth();
  const { loading, createFlutterwavePayment, createPaystackPayment, initiateCryptoPayment } = useCryptoPayments();
  const [activeProvider, setActiveProvider] = useState<string | null>(null);

  const handleFlutterwavePayment = async () => {
    try {
      setActiveProvider('flutterwave');
      const paymentLink = await createFlutterwavePayment({
        amount: plan.price,
        currency: 'NGN',
        plan: plan.tier,
        customer: {
          email: user?.email || '',
          name: user?.user_metadata?.full_name || 'User'
        }
      });
      window.open(paymentLink, '_blank');
    } catch (error) {
      toast.error('Failed to initialize Flutterwave payment');
    } finally {
      setActiveProvider(null);
    }
  };

  const handlePaystackPayment = async () => {
    try {
      setActiveProvider('paystack');
      const authUrl = await createPaystackPayment({
        amount: plan.price * 100, // Paystack expects amount in kobo
        email: user?.email || '',
        plan: plan.tier,
        currency: 'NGN'
      });
      window.open(authUrl, '_blank');
    } catch (error) {
      toast.error('Failed to initialize Paystack payment');
    } finally {
      setActiveProvider(null);
    }
  };

  const handleCryptoPayment = async (provider: 'bitcoin' | 'ethereum') => {
    try {
      setActiveProvider(provider);
      const result = await initiateCryptoPayment(provider, plan.price, plan.tier);
      
      toast.success(`${provider} payment initiated. Please send ${result.amount} ${result.currency} to: ${result.address}`);
      
      // You can show a modal with payment details here
      console.log('Crypto payment details:', result);
    } catch (error) {
      toast.error(`Failed to initialize ${provider} payment`);
    } finally {
      setActiveProvider(null);
    }
  };

  const paymentMethods = [
    {
      id: 'flutterwave',
      name: 'Flutterwave',
      description: 'Pay with cards, bank transfer, or mobile money',
      icon: CreditCard,
      handler: handleFlutterwavePayment,
      badge: 'Popular'
    },
    {
      id: 'paystack',
      name: 'Paystack',
      description: 'Secure card and bank payments',
      icon: CreditCard,
      handler: handlePaystackPayment,
      badge: null
    },
    {
      id: 'bitcoin',
      name: 'Bitcoin',
      description: 'Pay with Bitcoin (BTC)',
      icon: Bitcoin,
      handler: () => handleCryptoPayment('bitcoin'),
      badge: 'Crypto'
    },
    {
      id: 'ethereum',
      name: 'Ethereum',
      description: 'Pay with Ethereum (ETH)',
      icon: Wallet,
      handler: () => handleCryptoPayment('ethereum'),
      badge: 'Crypto'
    }
  ];

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold mb-4">Choose Payment Method</h3>
      
      {paymentMethods.map((method) => (
        <Card key={method.id} className="bg-gradient-card hover:shadow-lg transition-shadow">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <method.icon className="h-6 w-6 text-primary" />
                <div>
                  <CardTitle className="text-base">{method.name}</CardTitle>
                  <CardDescription className="text-sm">{method.description}</CardDescription>
                </div>
              </div>
              {method.badge && (
                <Badge variant="secondary" className="bg-primary/20 text-primary">
                  {method.badge}
                </Badge>
              )}
            </div>
          </CardHeader>
          <CardContent className="pt-0">
            <Button
              onClick={method.handler}
              disabled={loading}
              className="w-full"
              variant={method.id === 'flutterwave' ? 'default' : 'outline'}
            >
              {activeProvider === method.id ? (
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
              ) : (
                <method.icon className="h-4 w-4 mr-2" />
              )}
              Pay ${plan.price} with {method.name}
            </Button>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};
