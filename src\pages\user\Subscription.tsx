
import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Crown, Check, Star, Calendar, CreditCard, Loader2 } from 'lucide-react';
import { SubscribeModal, type PlanDef } from '@/components/payments/SubscribeModal';
import { useSubscription } from '@/hooks/useSubscription';
import { toast } from 'sonner';

const UserSubscription: React.FC = () => {
  const { 
    subscribed, 
    subscription_tier, 
    subscription_end, 
    loading, 
    error,
    createCheckout, 
    openCustomerPortal,
    checkSubscription 
  } = useSubscription();
  
  const [checkoutLoading, setCheckoutLoading] = useState<string | null>(null);
  const [portalLoading, setPortalLoading] = useState(false);

  const plans: Array<PlanDef & { features: string[]; current: boolean; popular?: boolean }> = [
    {
      name: "Basic",
      priceUSD: 9.99,
      tier: "basic",
      features: [
        "5 predictions per day",
        "Basic analytics",
        "Email support",
        "Mobile app access"
      ],
      current: subscription_tier === 'basic'
    },
    {
      name: "Premium",
      priceUSD: 19.99,
      tier: "premium",
      features: [
        "Unlimited predictions",
        "Advanced analytics",
        "Priority support",
        "Mobile app access",
        "Live notifications",
        "Expert insights"
      ],
      current: subscription_tier === 'premium',
      popular: true
    },
    {
      name: "Enterprise",
      priceUSD: 39.99,
      tier: "enterprise",
      features: [
        "Everything in Premium",
        "1-on-1 expert consultation",
        "Custom predictions",
        "VIP Discord access",
        "Early access to features",
        "API Access"
      ],
      current: subscription_tier === 'enterprise'
    }
  ];

  const [selectedPlan, setSelectedPlan] = useState<PlanDef | null>(null);
  const [modalOpen, setModalOpen] = useState(false);

  const openSubscribeModal = (plan: PlanDef) => {
    setSelectedPlan(plan);
    setModalOpen(true);
  };

  const handleManageSubscription = async () => {
    try {
      setPortalLoading(true);
      const url = await openCustomerPortal();
      window.open(url, '_blank');
    } catch (error) {
      console.error('Portal error:', error);
      toast.error('Failed to open customer portal');
    } finally {
      setPortalLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-hero flex items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-hero">
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8 text-center">
          <h1 className="text-3xl font-bruno tracking-wide mb-2">Subscription Plans</h1>
          <p className="text-muted-foreground">Choose the perfect plan for your betting strategy</p>
          <Button 
            variant="outline" 
            onClick={checkSubscription}
            className="mt-4"
            disabled={loading}
          >
            {loading ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : null}
            Refresh Status
          </Button>
        </div>

        {/* Current Subscription */}
        {subscribed && (
          <Card className="bg-gradient-card mb-8">
            <CardHeader>
              <div className="flex items-center gap-2">
                <Crown className="h-5 w-5 text-warning" />
                <CardTitle>Current Subscription</CardTitle>
              </div>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-xl font-semibold capitalize">{subscription_tier} Plan</h3>
                  <p className="text-muted-foreground">
                    {subscription_end ? `Next billing: ${new Date(subscription_end).toLocaleDateString()}` : 'Active subscription'}
                  </p>
                </div>
                <div className="text-right">
                  <Badge variant="secondary" className="bg-success/20 text-success">Active</Badge>
                </div>
              </div>
              <div className="flex gap-2 mt-4">
                <Button 
                  variant="outline"
                  onClick={() => toast.info('Subscription management will be available soon')}
                  disabled={true}
                >
                  <CreditCard className="h-4 w-4 mr-2" />
                  Manage Subscription
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Available Plans */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {plans.map((plan, index) => (
            <Card key={index} className={`bg-gradient-card relative ${plan.current ? 'ring-2 ring-primary' : ''}`}>
              {plan.popular && (
                <Badge className="absolute -top-3 left-1/2 transform -translate-x-1/2 bg-gradient-primary">
                  Most Popular
                </Badge>
              )}
              <CardHeader className="text-center">
                <CardTitle className="flex items-center justify-center gap-2">
                  {plan.current && <Crown className="h-5 w-5 text-warning" />}
                  {plan.name}
                </CardTitle>
                <div className="mt-4">
                <span className="text-3xl font-bold">${plan.priceUSD.toFixed(2)}</span>
                <span className="text-muted-foreground">/month</span>
                </div>
              </CardHeader>
              <CardContent>
                <ul className="space-y-3 mb-6">
                  {plan.features.map((feature, i) => (
                    <li key={i} className="flex items-center gap-2">
                      <Check className="h-4 w-4 text-success" />
                      <span className="text-sm">{feature}</span>
                    </li>
                  ))}
                </ul>
                <Button 
                  className="w-full" 
                  variant={plan.current ? "outline" : "default"}
                  disabled={plan.current}
                  onClick={() => openSubscribeModal(plan)}
                >
                  {plan.current ? "Current Plan" : "Subscribe"}
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>

        {error && (
          <Card className="bg-destructive/10 border-destructive/20 mt-8">
            <CardContent className="pt-6">
              <p className="text-destructive text-center">{error}</p>
            </CardContent>
          </Card>
        )}
      </div>
      <SubscribeModal open={modalOpen} onOpenChange={setModalOpen} plan={selectedPlan} />
    </div>
  );
};
 
export default UserSubscription;
