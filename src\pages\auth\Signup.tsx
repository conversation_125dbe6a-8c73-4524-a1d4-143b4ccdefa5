import React from 'react';
import { SignupForm } from '@/components/auth/SignupForm';
import { Ad<PERSON><PERSON>Header, AdSenseFooter } from '@/components/AdSense';
import { Link } from 'react-router-dom';
import { ArrowLeft } from 'lucide-react';
import { LogoAuth } from '@/components/Logo';

const Signup: React.FC = () => {
  return (
    <div className="min-h-screen bg-gradient-hero">
      <AdSenseHeader />
      
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-between mb-8">
          <Link 
            to="/" 
            className="flex items-center text-muted-foreground hover:text-foreground transition-colors"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Home
          </Link>
          
          <LogoAuth />
        </div>
        
        <div className="flex justify-center">
          <SignupForm />
        </div>
      </div>
      
      <AdSenseFooter className="mt-auto" />
    </div>
  );
};

export default Signup;