#!/bin/bash

# Sports Prediction Platform Deployment Script
set -e

echo "🚀 Starting deployment of Sports Prediction Platform..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    print_error "Docker is not installed. Please install Docker first."
    exit 1
fi

# Check if Docker Compose is installed
if ! command -v docker-compose &> /dev/null; then
    print_error "Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

# Create necessary directories
print_status "Creating necessary directories..."
mkdir -p backend/logs
mkdir -p data/postgres

# Check if .env files exist
if [ ! -f "backend/.env" ]; then
    print_warning "Backend .env file not found. Creating from example..."
    cp backend/.env.example backend/.env
    print_warning "Please update backend/.env with your actual configuration before running the application."
fi

if [ ! -f ".env" ]; then
    print_warning "Frontend .env file not found. Creating from example..."
    cp .env.example .env
    print_warning "Please update .env with your actual configuration before running the application."
fi

# Build and start services
print_status "Building and starting services..."
docker-compose down --remove-orphans
docker-compose build --no-cache
docker-compose up -d

# Wait for services to be healthy
print_status "Waiting for services to be healthy..."
sleep 30

# Check service health
print_status "Checking service health..."

# Check PostgreSQL
if docker-compose exec -T postgres pg_isready -U postgres > /dev/null 2>&1; then
    print_status "✅ PostgreSQL is healthy"
else
    print_error "❌ PostgreSQL is not healthy"
    exit 1
fi

# Check Redis
if docker-compose exec -T redis redis-cli ping > /dev/null 2>&1; then
    print_status "✅ Redis is healthy"
else
    print_warning "⚠️  Redis is not healthy (optional service)"
fi

# Check Backend
if curl -f http://localhost:3001/health > /dev/null 2>&1; then
    print_status "✅ Backend API is healthy"
else
    print_error "❌ Backend API is not healthy"
    docker-compose logs backend
    exit 1
fi

# Check Frontend
if curl -f http://localhost:80 > /dev/null 2>&1; then
    print_status "✅ Frontend is healthy"
else
    print_error "❌ Frontend is not healthy"
    docker-compose logs frontend
    exit 1
fi

# Run database migrations
print_status "Running database migrations..."
docker-compose exec backend npx prisma migrate deploy

# Seed database if needed
print_status "Seeding database..."
docker-compose exec backend npx tsx prisma/seed.ts

print_status "🎉 Deployment completed successfully!"
echo ""
echo "📊 Application URLs:"
echo "   Frontend: http://localhost:80"
echo "   Backend API: http://localhost:3001"
echo "   Health Check: http://localhost:3001/health"
echo ""
echo "🔐 Default Login Credentials:"
echo "   Admin: <EMAIL> / admin123"
echo "   Test User: <EMAIL> / test123"
echo ""
echo "📝 Next Steps:"
echo "   1. Update environment variables in backend/.env and .env"
echo "   2. Add your API keys for sports data providers"
echo "   3. Configure SSL certificates for production"
echo "   4. Set up monitoring and logging"
echo ""
echo "🛠️  Useful Commands:"
echo "   View logs: docker-compose logs -f [service]"
echo "   Stop services: docker-compose down"
echo "   Restart services: docker-compose restart"
echo "   Update services: docker-compose pull && docker-compose up -d"
