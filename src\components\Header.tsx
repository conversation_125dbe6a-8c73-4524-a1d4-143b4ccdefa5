import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { TrendingUp, Menu, X, User, Settings, LogOut } from 'lucide-react';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { useAuth } from '@/contexts/AuthContext';
import { ThemeToggle } from '@/components/ThemeToggle';
import { LogoHeader } from '@/components/Logo';
export const Header: React.FC = () => {
  const [isMenuOpen, setIsMenuOpen] = React.useState(false);
  const { user, isAdmin, signOut } = useAuth();
  const location = useLocation();
  const isActive = (path: string) => location.pathname === path;
  return <header className="bg-card/95 backdrop-blur-sm border-b border-border sticky top-0 z-50">
      <div className="container mx-auto px-4 py-4">
        <div className="flex items-center justify-between">
          <Link to="/" className="flex items-center space-x-2">
            {/* Desktop Logo */}
            <div className="hidden sm:flex">
              <LogoHeader />
            </div>
            {/* Mobile Logo */}
            <div className="sm:hidden">
              <LogoHeader className="scale-90" />
            </div>
          </Link>
          
          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-6">
            <Link to="/user/subscription" className={`transition-colors font-bruno uppercase ${isActive('/user/subscription') ? 'text-red-500' : 'text-red-500/80 hover:text-red-500'}`}>
              ELITE
            </Link>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <button className={`transition-colors font-bruno uppercase ${isActive('/predictions') ? 'text-red-500' : 'text-red-500/80 hover:text-red-500'}`}>PREDICTIONS</button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuItem asChild>
                  <Link to="/predictions">All Predictions</Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link to="/tips">League Tips</Link>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <button className={`transition-colors font-bruno uppercase text-red-500/80 hover:text-red-500`}>LEGAL</button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuItem asChild>
                  <Link to="/privacy">Privacy Policy</Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link to="/terms">Terms of Service</Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link to="/legal/age-verification">Age Verification</Link>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
            <Link to="/leagues" className={`transition-colors font-bruno uppercase ${isActive('/leagues') ? 'text-red-500' : 'text-red-500/80 hover:text-red-500'}`}>
              LEAGUES
            </Link>
            <Link to="/tips" className={`transition-colors font-bruno uppercase ${isActive('/tips') ? 'text-red-500' : 'text-red-500/80 hover:text-red-500'}`}>
              TIPS
            </Link>
            {isAdmin && <Link to="/admin" className={`transition-colors font-medium uppercase ${isActive('/admin') ? 'text-primary' : 'text-foreground hover:text-primary'}`}>
                ADMIN
              </Link>}
          </nav>

          {/* Action Buttons */}
          <div className="hidden md:flex items-center space-x-3 font-bruno">
            <ThemeToggle />
            {user ? <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="sm" className="flex items-center space-x-2">
                    <User className="h-4 w-4" />
                    <span>Account</span>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-48">
                  <DropdownMenuItem asChild>
                    <Link to="/user/dashboard" className="flex items-center">
                      <User className="h-4 w-4 mr-2" />
                      Dashboard
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem asChild>
                    <Link to="/user/settings" className="flex items-center">
                      <Settings className="h-4 w-4 mr-2" />
                      Settings
                    </Link>
                  </DropdownMenuItem>
                  {isAdmin && (
                    <>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem asChild>
                        <Link to="/admin/dashboard" className="flex items-center">
                          <Settings className="h-4 w-4 mr-2" />
                          Admin Panel
                        </Link>
                      </DropdownMenuItem>
                    </>
                  )}
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={signOut} className="flex items-center text-destructive">
                    <LogOut className="h-4 w-4 mr-2" />
                    Sign Out
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu> : <>
                <Button variant="ghost" size="sm" asChild>
                  <Link to="/auth/login">Sign In</Link>
                </Button>
                <Button size="sm" asChild>
                  <Link to="/auth/signup">Get Started</Link>
                </Button>
              </>}
          </div>

          {/* Mobile Menu Button */}
          <button className="md:hidden p-2" onClick={() => setIsMenuOpen(!isMenuOpen)}>
            {isMenuOpen ? <X className="h-6 w-6 text-foreground" /> : <Menu className="h-6 w-6 text-foreground" />}
          </button>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && <nav className="md:hidden mt-4 pb-4 border-t border-border pt-4 font-bruno">
            <div className="flex flex-col space-y-3">
              <Link to="/user/subscription" className={`transition-colors uppercase ${isActive('/user/subscription') ? 'text-red-500' : 'text-red-500/80 hover:text-red-500'}`} onClick={() => setIsMenuOpen(false)}>
                ELITE
              </Link>
              <Link to="/predictions" className={`transition-colors uppercase ${isActive('/predictions') ? 'text-red-500' : 'text-red-500/80 hover:text-red-500'}`} onClick={() => setIsMenuOpen(false)}>
                PREDICTIONS
              </Link>
              <Link to="/leagues" className={`transition-colors uppercase ${isActive('/leagues') ? 'text-red-500' : 'text-red-500/80 hover:text-red-500'}`} onClick={() => setIsMenuOpen(false)}>
                LEAGUES
              </Link>
              <Link to="/tips" className={`transition-colors uppercase ${isActive('/tips') ? 'text-red-500' : 'text-red-500/80 hover:text-red-500'}`} onClick={() => setIsMenuOpen(false)}>
                TIPS
              </Link>
              {isAdmin && <Link to="/admin" className={`transition-colors font-medium uppercase ${isActive('/admin') ? 'text-primary' : 'text-foreground hover:text-primary'}`} onClick={() => setIsMenuOpen(false)}>
                  ADMIN
                </Link>}
              <div className="flex flex-col space-y-2 pt-2">
                {user ? <>
                    <Button variant="ghost" size="sm" className="w-full justify-start" asChild>
                      <Link to="/user/dashboard">
                        <User className="h-4 w-4 mr-2" />
                        Dashboard
                      </Link>
                    </Button>
                    <Button variant="ghost" size="sm" className="w-full justify-start" asChild>
                      <Link to="/user/settings">
                        <Settings className="h-4 w-4 mr-2" />
                        Settings
                      </Link>
                    </Button>
                    {isAdmin && (
                      <Button variant="ghost" size="sm" className="w-full justify-start" asChild>
                        <Link to="/admin/dashboard">
                          <Settings className="h-4 w-4 mr-2" />
                          Admin Panel
                        </Link>
                      </Button>
                    )}
                    <Button variant="ghost" size="sm" className="w-full justify-start text-destructive" onClick={signOut}>
                      <LogOut className="h-4 w-4 mr-2" />
                      Sign Out
                    </Button>
                  </> : <>
                    <Button variant="ghost" size="sm" className="w-full" asChild>
                      <Link to="/auth/login">Sign In</Link>
                    </Button>
                    <Button size="sm" className="w-full" asChild>
                      <Link to="/auth/signup">Get Started</Link>
                    </Button>
                  </>}
              </div>
            </div>
          </nav>}
      </div>
    </header>;
};