
import { createContext, useContext, useEffect, useState } from 'react'
import { apiService as api, User, ApiResponse } from '@/services/api'

interface AuthError {
  message: string
}

interface AuthContextType {
  user: User | null
  loading: boolean
  signUp: (email: string, password: string, fullName?: string) => Promise<{ error: AuthError | null }>
  signIn: (email: string, password: string) => Promise<{ error: AuthError | null }>
  signOut: () => Promise<void>
  updateProfile: (updates: Partial<User>) => Promise<void>
  isAdmin: boolean
  isSuperAdmin: boolean
  refreshProfile: () => Promise<void>
  isAuthenticated: boolean
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)

  const refreshProfile = async () => {
    try {
      const response = await api.getProfile()
      if (response.success && response.data) {
        setUser(response.data)
      } else {
        setUser(null)
      }
    } catch (error) {
      console.error('Error fetching profile:', error)
      setUser(null)
    }
  }

  useEffect(() => {
    // Check if user is already logged in
    const initializeAuth = async () => {
      setLoading(true)

      if (api.isAuthenticated()) {
        const currentUser = api.getCurrentUser()
        if (currentUser) {
          setUser(currentUser)
        } else {
          // Try to refresh profile from server
          await refreshProfile()
        }
      }

      setLoading(false)
    }

    initializeAuth()
  }, [])

  const signUp = async (email: string, password: string, fullName?: string) => {
    try {
      const response = await api.register({ email, password, fullName })

      if (response.success && response.data) {
        setUser(response.data.user)
        return { error: null }
      } else {
        return { error: { message: response.error || 'Registration failed' } }
      }
    } catch (error) {
      return { error: { message: error instanceof Error ? error.message : 'Registration failed' } }
    }
  }

  const signIn = async (email: string, password: string) => {
    try {
      const response = await api.login(email, password)

      if (response.success && response.data) {
        setUser(response.data.user)
        return { error: null }
      } else {
        return { error: { message: response.error || 'Login failed' } }
      }
    } catch (error) {
      return { error: { message: error instanceof Error ? error.message : 'Login failed' } }
    }
  }

  const signOut = async () => {
    await api.logout()
    setUser(null)
  }

  const updateProfile = async (updates: Partial<User>) => {
    if (!user) return

    try {
      // Update profile via API (this would need to be implemented in the backend)
      // For now, just update local state
      setUser({ ...user, ...updates })
      await refreshProfile()
    } catch (error) {
      console.error('Error updating profile:', error)
      throw error
    }
  }

  const isAdmin = user?.role === 'ADMIN' || user?.role === 'SUPER_ADMIN'
  const isSuperAdmin = user?.role === 'SUPER_ADMIN'
  const isAuthenticated = !!user && api.isAuthenticated()

  const value = {
    user,
    loading,
    signUp,
    signIn,
    signOut,
    updateProfile,
    isAdmin,
    isSuperAdmin,
    refreshProfile,
    isAuthenticated,
  }

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}
