import React from 'react';
import { Header } from '@/components/Header';
import <PERSON><PERSON>Footer from '@/components/MinimalFooter';
import { SEOHead } from '@/components/SEOHead';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { Card } from '@/components/ui/card';
import { HelpCircle, Users, CreditCard, Shield, TrendingUp, Target } from 'lucide-react';

const FAQ: React.FC = () => {
  const faqCategories = [
    {
      icon: <HelpCircle className="w-6 h-6" />,
      title: "General Questions",
      faqs: [
        {
          question: "What is 1300BLK AI Sports Engine?",
          answer: "1300BLK AI is an advanced sports prediction platform that uses machine learning algorithms to analyze sports data and provide accurate predictions across 80+ markets. Our AI processes vast amounts of historical data, player statistics, team performance, and real-time factors to deliver predictions with up to 94% accuracy."
        },
        {
          question: "How accurate are your predictions?",
          answer: "Our AI model achieves an average accuracy rate of 94% across all sports markets. However, accuracy can vary by sport, league, and market type. We provide confidence scores for each prediction to help you understand the reliability of our forecasts."
        },
        {
          question: "Which sports do you cover?",
          answer: "We cover major sports including Football (NFL, College), Basketball (NBA, NCAAB), Baseball (MLB), Tennis, Soccer, Hockey (NHL), and many more. Our platform analyzes over 80 different betting markets across these sports."
        },
        {
          question: "How often are predictions updated?",
          answer: "Our predictions are updated in real-time as new data becomes available. We continuously monitor player injuries, weather conditions, lineup changes, and other factors that could impact game outcomes."
        }
      ]
    },
    {
      icon: <Users className="w-6 h-6" />,
      title: "Account & Subscription",
      faqs: [
        {
          question: "Do I need an account to view predictions?",
          answer: "Basic predictions are available to all visitors. However, creating a free account gives you access to detailed analysis, historical data, and the ability to track your prediction performance. Premium subscribers get access to advanced features and exclusive markets."
        },
        {
          question: "What's included in the premium subscription?",
          answer: "Premium subscribers get access to: Advanced AI predictions with higher accuracy, Exclusive VIP picks, Live betting alerts, Detailed statistical analysis, Multi-sport parlays, Priority customer support, and Ad-free experience."
        },
        {
          question: "Can I cancel my subscription anytime?",
          answer: "Yes, you can cancel your subscription at any time from your account settings. You'll retain access to premium features until the end of your current billing period."
        },
        {
          question: "Is there a free trial available?",
          answer: "We offer a 7-day free trial for new users to experience our premium features. No credit card is required to start your trial."
        }
      ]
    },
    {
      icon: <TrendingUp className="w-6 h-6" />,
      title: "Predictions & Analytics",
      faqs: [
        {
          question: "How do you generate predictions?",
          answer: "Our AI analyzes historical performance data, player statistics, team dynamics, injury reports, weather conditions, and hundreds of other variables. The machine learning model continuously learns and improves from past predictions and outcomes."
        },
        {
          question: "What is a confidence score?",
          answer: "The confidence score (displayed as a percentage) indicates how certain our AI is about a particular prediction. Higher confidence scores suggest more reliable predictions based on historical patterns and data analysis."
        },
        {
          question: "Can I see your prediction history?",
          answer: "Yes, we maintain complete transparency by showing our prediction history and performance metrics. Premium users can access detailed analytics showing our win rates across different sports and markets."
        },
        {
          question: "Do you provide live betting recommendations?",
          answer: "Premium subscribers receive real-time alerts for live betting opportunities when our AI identifies favorable odds during games. These alerts are sent via email and in-app notifications."
        }
      ]
    },
    {
      icon: <Shield className="w-6 h-6" />,
      title: "Security & Privacy",
      faqs: [
        {
          question: "Is my personal information secure?",
          answer: "Absolutely. We use industry-standard encryption and security protocols to protect your personal information. We never share your data with third parties without your explicit consent."
        },
        {
          question: "How do you handle payment information?",
          answer: "All payment processing is handled through secure, PCI-compliant payment processors. We don't store your credit card information on our servers."
        },
        {
          question: "Can I delete my account?",
          answer: "Yes, you can request account deletion at any time by contacting our support team. We'll permanently remove all your personal data from our systems within 30 days."
        },
        {
          question: "Do you use cookies?",
          answer: "We use essential cookies to ensure our website functions properly and optional cookies to improve your experience. You can manage your cookie preferences in your browser settings."
        }
      ]
    },
    {
      icon: <Target className="w-6 h-6" />,
      title: "Responsible Gaming",
      faqs: [
        {
          question: "Do you promote responsible gambling?",
          answer: "Yes, we strongly advocate for responsible gambling. Our predictions are for informational and entertainment purposes. We provide resources for problem gambling and encourage users to set limits and gamble responsibly."
        },
        {
          question: "Are you licensed for gambling operations?",
          answer: "We are a sports analytics and prediction service, not a gambling operator. We don't accept bets or facilitate gambling transactions. Users must use licensed sportsbooks in their jurisdiction."
        },
        {
          question: "What age restrictions apply?",
          answer: "Our service is only available to users 18 years or older (21+ in some jurisdictions). We verify age during registration and comply with all applicable laws."
        },
        {
          question: "How can I get help with gambling addiction?",
          answer: "If you or someone you know has a gambling problem, please contact: National Problem Gambling Helpline: 1-800-522-4700, or visit ncpgambling.org for resources and support."
        }
      ]
    }
  ];

  return (
    <>
      <SEOHead
        title="FAQ - Frequently Asked Questions | 1300BLK AI Sports Engine"
        description="Get answers to frequently asked questions about 1300BLK AI Sports Engine. Learn about our predictions, subscriptions, security, and responsible gaming policies."
        keywords="FAQ, sports predictions help, AI betting questions, 1300BLK AI support, sports analytics FAQ"
      />

      <div className="min-h-screen bg-gradient-hero">
        <Header />
        
        <main className="container mx-auto px-4 py-16">
          {/* Hero Section */}
          <div className="text-center mb-16">
            <h1 className="text-4xl md:text-6xl font-orbitron font-black mb-6">
              <span className="bg-gradient-primary bg-clip-text text-transparent">
                Frequently Asked
              </span>
              <br />
              <span className="text-foreground">Questions</span>
            </h1>
            <p className="text-xl text-muted-foreground font-exo max-w-3xl mx-auto">
              Find answers to common questions about our AI sports prediction platform, 
              subscriptions, and features.
            </p>
          </div>

          {/* FAQ Categories */}
          <div className="space-y-8">
            {faqCategories.map((category, categoryIndex) => (
              <Card key={categoryIndex} className="bg-card/80 backdrop-blur-sm border-border/50 p-6">
                <div className="flex items-center gap-3 mb-6">
                  <div className="p-2 bg-primary/10 rounded-lg text-primary">
                    {category.icon}
                  </div>
                  <h2 className="text-2xl font-rajdhani font-bold text-foreground">
                    {category.title}
                  </h2>
                </div>

                <Accordion type="single" collapsible className="space-y-2">
                  {category.faqs.map((faq, faqIndex) => (
                    <AccordionItem 
                      key={faqIndex} 
                      value={`${categoryIndex}-${faqIndex}`}
                      className="border border-border/30 rounded-lg px-4"
                    >
                      <AccordionTrigger className="text-left font-rajdhani font-semibold hover:text-primary">
                        {faq.question}
                      </AccordionTrigger>
                      <AccordionContent className="text-muted-foreground font-exo leading-relaxed pt-2">
                        {faq.answer}
                      </AccordionContent>
                    </AccordionItem>
                  ))}
                </Accordion>
              </Card>
            ))}
          </div>

          {/* Contact CTA */}
          <Card className="bg-gradient-primary/10 border-primary/20 backdrop-blur-sm p-8 mt-16 text-center">
            <h3 className="text-2xl font-orbitron font-bold mb-4 text-foreground">
              Still Have Questions?
            </h3>
            <p className="text-muted-foreground font-exo mb-6 max-w-2xl mx-auto">
              Can't find what you're looking for? Our support team is here to help you 
              with any questions about our platform or services.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a 
                href="/contact" 
                className="inline-flex items-center justify-center px-6 py-3 bg-primary text-white font-rajdhani font-semibold rounded-lg hover:bg-primary/90 transition-colors"
              >
                Contact Support
              </a>
              <a 
                href="mailto:<EMAIL>" 
                className="inline-flex items-center justify-center px-6 py-3 border border-primary text-primary font-rajdhani font-semibold rounded-lg hover:bg-primary/10 transition-colors"
              >
                Email Us
              </a>
            </div>
          </Card>
        </main>

        <MinimalFooter />
      </div>
    </>
  );
};

export default FAQ;