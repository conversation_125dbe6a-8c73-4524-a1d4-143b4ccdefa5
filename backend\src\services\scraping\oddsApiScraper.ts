import { BaseScraper, ScrapingResult, MatchData } from './baseScraper';
import { logger } from '@/utils/logger';

interface OddsAPIGame {
  id: string;
  sport_key: string;
  sport_title: string;
  commence_time: string;
  home_team: string;
  away_team: string;
  bookmakers: Array<{
    key: string;
    title: string;
    last_update: string;
    markets: Array<{
      key: string;
      outcomes: Array<{
        name: string;
        price: number;
        point?: number;
      }>;
    }>;
  }>;
}

export class OddsAPIScraper extends BaseScraper {
  private sportMappings: { [key: string]: string } = {
    'football': 'americanfootball_nfl',
    'nfl': 'americanfootball_nfl',
    'basketball': 'basketball_nba',
    'nba': 'basketball_nba',
    'baseball': 'baseball_mlb',
    'mlb': 'baseball_mlb',
    'hockey': 'icehockey_nhl',
    'nhl': 'icehockey_nhl',
    'soccer': 'soccer_usa_mls',
    'mls': 'soccer_usa_mls',
  };

  constructor() {
    super('The Odds API', 'https://api.the-odds-api.com/v4/sports', process.env.ODDS_API_KEY);
  }

  async scrapeMatches(sport: string, league?: string): Promise<ScrapingResult> {
    try {
      if (!this.apiKey) {
        return this.createResult(false, [], 'The Odds API key not configured');
      }

      const oddsApiSport = this.mapSportToOddsAPI(sport, league);
      if (!oddsApiSport) {
        return this.createResult(false, [], `Unsupported sport/league: ${sport}/${league}`);
      }

      const url = `${this.baseUrl}/${oddsApiSport}/odds`;
      const params = new URLSearchParams({
        apiKey: this.apiKey,
        regions: 'us',
        markets: 'h2h,spreads,totals',
        oddsFormat: 'american',
        dateFormat: 'iso',
      });

      logger.info(`Odds API: Scraping ${url}?${params}`);

      const data: OddsAPIGame[] = await this.makeRequest(`${url}?${params}`);
      
      if (!Array.isArray(data)) {
        return this.createResult(false, [], 'Invalid response format from Odds API');
      }

      const matches: MatchData[] = [];

      for (const game of data) {
        try {
          const match = this.parseOddsAPIGame(game, sport, league);
          if (match && this.validateMatchData(match)) {
            matches.push(match);
          }
        } catch (error) {
          logger.warn(`Odds API: Failed to parse game ${game.id}: ${error}`);
        }
      }

      logger.info(`Odds API: Successfully scraped ${matches.length} matches for ${sport}/${league}`);
      return this.createResult(true, matches);

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      logger.error(`Odds API: Scraping failed: ${errorMessage}`);
      return this.createResult(false, [], errorMessage);
    }
  }

  private parseOddsAPIGame(game: OddsAPIGame, sport: string, league?: string): MatchData | null {
    const matchDate = new Date(game.commence_time);
    const homeTeamName = this.normalizeTeamName(game.home_team);
    const awayTeamName = this.normalizeTeamName(game.away_team);
    const leagueName = league || this.extractLeagueFromSport(game.sport_key);

    const match: MatchData = {
      matchId: this.generateMatchId(homeTeamName, awayTeamName, matchDate, leagueName),
      sport,
      league: leagueName.toUpperCase(),
      homeTeam: homeTeamName,
      awayTeam: awayTeamName,
      matchDate,
      source: 'The Odds API',
      status: 'UPCOMING',
    };

    // Parse odds from bookmakers
    if (game.bookmakers && game.bookmakers.length > 0) {
      match.odds = this.parseOddsAPIBookmakers(game.bookmakers, homeTeamName, awayTeamName);
    }

    // Generate basic stats
    match.stats = {
      home_rating: this.generateRating(homeTeamName),
      away_rating: this.generateRating(awayTeamName),
    };

    return match;
  }

  private parseOddsAPIBookmakers(bookmakers: any[], homeTeam: string, awayTeam: string): any {
    const result: any = {};

    // Use the first available bookmaker (usually DraftKings or FanDuel)
    const bookmaker = bookmakers[0];
    if (!bookmaker || !bookmaker.markets) {
      return result;
    }

    for (const market of bookmaker.markets) {
      switch (market.key) {
        case 'h2h': // Moneyline
          result.moneyline = this.parseMoneyline(market.outcomes, homeTeam, awayTeam);
          break;
        case 'spreads': // Point spread
          result.spread = this.parseSpread(market.outcomes, homeTeam, awayTeam);
          break;
        case 'totals': // Over/Under
          result.total = this.parseTotal(market.outcomes);
          break;
      }
    }

    return result;
  }

  private parseMoneyline(outcomes: any[], homeTeam: string, awayTeam: string): any {
    const result: any = {};
    
    for (const outcome of outcomes) {
      if (this.isTeamMatch(outcome.name, homeTeam)) {
        result.home = outcome.price;
      } else if (this.isTeamMatch(outcome.name, awayTeam)) {
        result.away = outcome.price;
      }
    }

    return result;
  }

  private parseSpread(outcomes: any[], homeTeam: string, awayTeam: string): any {
    const result: any = {};
    
    for (const outcome of outcomes) {
      if (this.isTeamMatch(outcome.name, homeTeam)) {
        result.home = outcome.price;
        result.line = outcome.point || 0;
      } else if (this.isTeamMatch(outcome.name, awayTeam)) {
        result.away = outcome.price;
      }
    }

    return result;
  }

  private parseTotal(outcomes: any[]): any {
    const result: any = {};
    
    for (const outcome of outcomes) {
      if (outcome.name === 'Over') {
        result.over = outcome.price;
        result.line = outcome.point || 0;
      } else if (outcome.name === 'Under') {
        result.under = outcome.price;
      }
    }

    return result;
  }

  private isTeamMatch(outcomeName: string, teamName: string): boolean {
    const normalizedOutcome = outcomeName.toLowerCase().replace(/[^\w\s]/g, '');
    const normalizedTeam = teamName.toLowerCase().replace(/[^\w\s]/g, '');
    
    // Check if team name is contained in outcome name
    return normalizedOutcome.includes(normalizedTeam) || 
           normalizedTeam.includes(normalizedOutcome);
  }

  private mapSportToOddsAPI(sport: string, league?: string): string | null {
    const key = league ? `${sport}_${league}`.toLowerCase() : sport.toLowerCase();
    return this.sportMappings[key] || this.sportMappings[sport.toLowerCase()] || null;
  }

  private extractLeagueFromSport(sportKey: string): string {
    const parts = sportKey.split('_');
    if (parts.length > 1) {
      return parts[parts.length - 1].toUpperCase();
    }
    return 'UNKNOWN';
  }

  private generateRating(teamName: string): number {
    // Simple hash-based rating generation for demo purposes
    let hash = 0;
    for (let i = 0; i < teamName.length; i++) {
      const char = teamName.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash;
    }
    return Math.abs(hash % 30) + 70; // Rating between 70-100
  }
}
