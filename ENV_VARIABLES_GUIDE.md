# 🔐 Environment Variables Complete Guide

## **📋 Required Environment Variables**

### **🔗 API & Backend Configuration**
```bash
# Backend API URL
VITE_API_BASE_URL=http://localhost:3004/api  # Development
VITE_API_BASE_URL=https://your-backend.vercel.app/api  # Production

# Node Environment
NODE_ENV=development  # or production
VITE_NODE_ENV=development  # or production

# Server Configuration
PORT=3004
CORS_ORIGIN=http://localhost:8080  # Development
CORS_ORIGIN=https://1300blk.online  # Production
```

### **🗄️ Database Configuration**
```bash
# Development (SQLite)
DATABASE_URL="file:./dev.db"

# Production (PostgreSQL - Vercel Postgres recommended)
DATABASE_URL="postgresql://user:password@host:port/database"

# Optional Database Settings
DB_POOL_SIZE=10
DB_TIMEOUT=30000
DB_SSL=true
```

### **🔐 Authentication & Security**
```bash
# JWT Configuration
JWT_SECRET="your-super-secret-jwt-key-change-this-in-production"
JWT_EXPIRES_IN="7d"

# Security Settings
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
```

### **💳 Payment Providers**

#### **Paystack Configuration**
```bash
# Public key (frontend)
VITE_PAYSTACK_PUBLIC_KEY="pk_test_your_paystack_public_key"  # Test
VITE_PAYSTACK_PUBLIC_KEY="pk_live_your_paystack_live_key"   # Live

# Secret key (backend only)
PAYSTACK_SECRET_KEY="sk_test_your_paystack_secret_key"      # Test
PAYSTACK_SECRET_KEY="sk_live_your_paystack_live_secret"     # Live
```

#### **Flutterwave Configuration**
```bash
# Public key (frontend)
VITE_FLUTTERWAVE_PUBLIC_KEY="FLWPUBK_TEST-your_public_key"  # Test
VITE_FLUTTERWAVE_PUBLIC_KEY="FLWPUBK-your_live_public_key"  # Live

# Secret key (backend only)
FLUTTERWAVE_SECRET_KEY="FLWSECK_TEST-your_secret_key"       # Test
FLUTTERWAVE_SECRET_KEY="FLWSECK-your_live_secret_key"       # Live
```

#### **Crypto Payment Configuration**
```bash
# Wallet address (frontend)
VITE_CRYPTO_WALLET_ADDRESS="your_crypto_wallet_address"

# API key (backend only)
CRYPTO_API_KEY="your_crypto_api_key"
```

### **🏈 Sports Data APIs**
```bash
# ESPN API
ESPN_API_KEY="your_espn_api_key"

# The Odds API
ODDS_API_KEY="your_odds_api_key"

# Sports Reference API
SPORTS_REFERENCE_API_KEY="your_sports_reference_api_key"

# SportsRadar API
SPORTS_RADAR_API_KEY="your_sports_radar_api_key"

# RapidAPI
RAPID_API_KEY="your_rapid_api_key"
```

### **🕷️ Web Scraping**
```bash
# ScraperAPI
SCRAPERAPI_KEY="your_scraperapi_key"

# Proxy Configuration
PROXY_URL="your_proxy_url"
```

### **📧 Email Configuration**
```bash
# SMTP Settings (for notifications)
SMTP_HOST="smtp.gmail.com"
SMTP_PORT="587"
SMTP_USER="<EMAIL>"
SMTP_PASS="your_app_password"
```

### **🚀 Redis (Caching)**
```bash
# Development
REDIS_URL="redis://localhost:6379"

# Production (Redis Cloud recommended)
REDIS_URL="redis://your_production_redis_url"
```

### **📊 Logging**
```bash
LOG_LEVEL="info"
LOG_FILE="logs/app.log"
```

## **🔧 How to Set Environment Variables**

### **Development**
1. Copy `.env.example` to `.env`
2. Fill in your development values
3. Never commit `.env` to version control

### **Production (Vercel)**
1. Go to Vercel Dashboard
2. Select your project
3. Go to Settings → Environment Variables
4. Add each variable with production values

### **Backend Environment Variables (Vercel)**
Required for backend deployment:
```bash
DATABASE_URL=postgresql://your_production_database_url
JWT_SECRET=your_production_jwt_secret
NODE_ENV=production
CORS_ORIGIN=https://1300blk.online
PAYSTACK_SECRET_KEY=sk_live_your_paystack_live_secret
FLUTTERWAVE_SECRET_KEY=FLWSECK-your_flutterwave_live_secret
ESPN_API_KEY=your_espn_api_key
ODDS_API_KEY=your_odds_api_key
SPORTS_REFERENCE_API_KEY=your_sports_reference_api_key
SPORTS_RADAR_API_KEY=your_sports_radar_api_key
RAPID_API_KEY=your_rapid_api_key
SCRAPERAPI_KEY=your_scraperapi_key
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_production_app_password
REDIS_URL=redis://your_production_redis_url
```

## **🔐 Security Best Practices**

1. **Never commit secrets**: Use `.env` files and `.gitignore`
2. **Use different keys**: Separate test/development from production
3. **Rotate secrets**: Regularly update API keys and secrets
4. **Least privilege**: Only grant necessary permissions
5. **Monitor usage**: Track API usage and set up alerts

## **📝 Getting API Keys**

### **Payment Providers**
- **Paystack**: https://paystack.com/
- **Flutterwave**: https://flutterwave.com/

### **Sports Data**
- **ESPN**: Contact ESPN for API access
- **The Odds API**: https://the-odds-api.com/
- **Sports Reference**: https://www.sports-reference.com/
- **SportsRadar**: https://sportradar.com/

### **Utilities**
- **RapidAPI**: https://rapidapi.com/
- **ScraperAPI**: https://scraperapi.com/
- **Redis Cloud**: https://redis.com/

## **✅ Environment Setup Checklist**

- [ ] Database configured and connected
- [ ] JWT secret set (strong, unique)
- [ ] Payment provider keys configured
- [ ] Sports API keys added
- [ ] Email SMTP configured
- [ ] Redis configured (optional but recommended)
- [ ] All environment variables set in Vercel dashboard
- [ ] Test environment variables work
- [ ] Production environment variables secured

**Note**: Replace all placeholder values with your actual API keys and secrets before deploying to production.
