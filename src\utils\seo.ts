
export interface SEOMetadata {
  title: string;
  description: string;
  keywords?: string;
  canonical?: string;
  ogImage?: string;
  ogType?: string;
}

export const generateSEOMetadata = (metadata: SEOMetadata) => {
  const baseUrl = 'https://1300blk.online';
  
  return {
    title: metadata.title,
    description: metadata.description,
    keywords: metadata.keywords || 'sports predictions, betting analysis, AI predictions, machine learning sports, football predictions, basketball betting, tennis predictions, sports analytics, live odds, betting tips',
    canonical: metadata.canonical || baseUrl,
    openGraph: {
      title: metadata.title,
      description: metadata.description,
      url: metadata.canonical || baseUrl,
      type: metadata.ogType || 'website',
      images: [
        {
          url: metadata.ogImage || `${baseUrl}/og-sports-predictions.jpg`,
          width: 1200,
          height: 630,
          alt: metadata.title
        }
      ],
      siteName: 'SportsPredictions'
    },
    twitter: {
      card: 'summary_large_image',
      title: metadata.title,
      description: metadata.description,
      images: [metadata.ogImage || `${baseUrl}/og-sports-predictions.jpg`],
      site: '@SportsPredictions',
      creator: '@SportsPredictions'
    }
  };
};

export const generateBreadcrumbSchema = (breadcrumbs: Array<{name: string, url: string}>) => {
  return {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": breadcrumbs.map((crumb, index) => ({
      "@type": "ListItem",
      "position": index + 1,
      "name": crumb.name,
      "item": crumb.url
    }))
  };
};

export const generateSportsEventSchema = (event: {
  name: string;
  startDate: string;
  location: string;
  homeTeam: string;
  awayTeam: string;
  sport: string;
  league: string;
}) => {
  return {
    "@context": "https://schema.org",
    "@type": "SportsEvent",
    "name": event.name,
    "startDate": event.startDate,
    "location": {
      "@type": "Place",
      "name": event.location
    },
    "homeTeam": {
      "@type": "SportsTeam",
      "name": event.homeTeam
    },
    "awayTeam": {
      "@type": "SportsTeam", 
      "name": event.awayTeam
    },
    "sport": event.sport,
    "tournament": {
      "@type": "SportsOrganization",
      "name": event.league
    }
  };
};

export const generateOrganizationSchema = () => {
  return {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "SportsPredictions",
    "description": "AI-powered sports predictions and betting analytics platform",
    "url": "https://1300blk.online",
    "logo": "https://1300blk.online/logo.png",
    "sameAs": [
      "https://twitter.com/SportsPredictions",
      "https://facebook.com/SportsPredictions",
      "https://instagram.com/SportsPredictions"
    ],
    "contactPoint": {
      "@type": "ContactPoint",
      "contactType": "customer service",
      "email": "<EMAIL>"
    }
  };
};

export const generateWebAppSchema = () => {
  return {
    "@context": "https://schema.org",
    "@type": "WebApplication",
    "name": "SportsPredictions",
    "description": "AI-powered sports predictions and betting analytics",
    "url": "https://1300blk.online",
    "applicationCategory": "Sports",
    "operatingSystem": "Web Browser",
    "offers": {
      "@type": "Offer",
      "price": "9.99",
      "priceCurrency": "USD",
      "priceValidUntil": "2025-12-31"
    },
    "aggregateRating": {
      "@type": "AggregateRating",
      "ratingValue": "4.8",
      "reviewCount": "1250"
    }
  };
};
