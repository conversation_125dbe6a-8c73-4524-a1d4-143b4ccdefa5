import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Clock, Target, TrendingUp } from 'lucide-react';
import { Skeleton } from '@/components/ui/skeleton';

interface Prediction {
  id: string;
  homeTeam: string;
  awayTeam: string;
  league: string;
  sport: string;
  startTime: string;
  market: string;
  prediction: string;
  confidence: number;
  odds: number;
  edge: number;
  source: 'ML' | 'MANUAL';
}

interface PredictionTableMobileProps {
  predictions: Prediction[];
  loading?: boolean;
}

const PredictionTableMobile: React.FC<PredictionTableMobileProps> = ({ 
  predictions, 
  loading = false 
}) => {
  if (loading) {
    return (
      <div className="space-y-4">
        {[1, 2, 3].map((i) => (
          <Card key={i} className="bg-gradient-card">
            <CardContent className="p-4">
              <div className="space-y-3">
                <div className="flex justify-between items-start">
                  <Skeleton className="h-5 w-32" />
                  <Skeleton className="h-4 w-16" />
                </div>
                <Skeleton className="h-4 w-full" />
                <div className="flex justify-between items-center">
                  <Skeleton className="h-6 w-20" />
                  <Skeleton className="h-8 w-24" />
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {predictions.map((prediction) => (
        <Card key={prediction.id} className="bg-gradient-card">
          <CardContent className="p-4">
            <div className="space-y-3">
              {/* Header */}
              <div className="flex justify-between items-start">
                <div>
                  <h3 className="font-semibold text-sm">
                    {prediction.homeTeam} vs {prediction.awayTeam}
                  </h3>
                  <p className="text-xs text-muted-foreground">
                    {prediction.league} • {prediction.sport}
                  </p>
                </div>
                <Badge variant="outline" className="text-xs">
                  <Clock className="h-3 w-3 mr-1" />
                  {prediction.startTime}
                </Badge>
              </div>

              {/* Market & Prediction */}
              <div className="space-y-2">
                <div className="text-sm">
                  <span className="text-muted-foreground">Market: </span>
                  <span className="font-medium">{prediction.market}</span>
                </div>
                <div className="text-sm">
                  <span className="text-muted-foreground">Prediction: </span>
                  <span className="font-semibold text-primary">{prediction.prediction}</span>
                </div>
              </div>

              {/* Stats */}
              <div className="flex justify-between items-center pt-2 border-t border-border">
                <div className="flex items-center gap-4 text-xs">
                  <div className="flex items-center gap-1">
                    <Target className="h-3 w-3 text-success" />
                    <span>{prediction.confidence}%</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <TrendingUp className="h-3 w-3 text-info" />
                    <span>{prediction.odds}</span>
                  </div>
                  <div className="text-warning">
                    +{prediction.edge}%
                  </div>
                </div>
                
                <Badge 
                  variant={prediction.source === 'ML' ? 'default' : 'secondary'}
                  className="text-xs"
                >
                  {prediction.source}
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};

export default PredictionTableMobile;