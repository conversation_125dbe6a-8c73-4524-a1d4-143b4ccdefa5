import React, { useEffect, useState } from 'react';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';

// Minimal consent banner for Google Consent Mode v2.
// NOTE: For EEA compliance for ads, use a Google-certified CMP (e.g. Funding Choices)
// This banner controls Google Consent Mode while your CMP loads / as a fallback.

const STORAGE_KEY = 'consent.v1';

type ConsentPrefs = {
  ad_storage: 'granted' | 'denied';
  ad_user_data: 'granted' | 'denied';
  ad_personalization: 'granted' | 'denied';
  analytics_storage: 'granted' | 'denied';
};

const defaultPrefs: ConsentPrefs = {
  ad_storage: 'denied',
  ad_user_data: 'denied',
  ad_personalization: 'denied',
  analytics_storage: 'denied',
};

function updateGtag(prefs: ConsentPrefs) {
  if (typeof window === 'undefined' || !(window as any).gtag) return;
  (window as any).gtag('consent', 'update', {
    ad_storage: prefs.ad_storage,
    ad_user_data: prefs.ad_user_data,
    ad_personalization: prefs.ad_personalization,
    analytics_storage: prefs.analytics_storage,
  });
}

export const ConsentBanner: React.FC = () => {
  const [visible, setVisible] = useState(false);
  const [prefs, setPrefs] = useState<ConsentPrefs>(defaultPrefs);

  useEffect(() => {
    try {
      const saved = localStorage.getItem(STORAGE_KEY);
      if (saved) {
        const parsed = JSON.parse(saved) as ConsentPrefs;
        setPrefs(parsed);
        updateGtag(parsed);
      } else {
        // Show banner by default (especially for EEA). Your CMP should also run.
        setVisible(true);
      }
    } catch {}
  }, []);

  const acceptAll = () => {
    const accepted: ConsentPrefs = {
      ad_storage: 'granted',
      ad_user_data: 'granted',
      ad_personalization: 'granted',
      analytics_storage: 'granted',
    };
    setPrefs(accepted);
    updateGtag(accepted);
    localStorage.setItem(STORAGE_KEY, JSON.stringify(accepted));
    setVisible(false);
  };

  const rejectAll = () => {
    setPrefs(defaultPrefs);
    updateGtag(defaultPrefs);
    localStorage.setItem(STORAGE_KEY, JSON.stringify(defaultPrefs));
    setVisible(false);
  };

  const saveChoices = () => {
    updateGtag(prefs);
    localStorage.setItem(STORAGE_KEY, JSON.stringify(prefs));
    setVisible(false);
  };

  if (!visible) return null;

  return (
    <div className="fixed inset-x-0 bottom-0 z-[100] bg-card/95 backdrop-blur border-t">
      <div className="container mx-auto px-4 py-4">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div className="text-sm text-foreground">
            <p className="font-semibold">We value your privacy</p>
            <p className="text-muted-foreground">
              We and our partners use cookies and similar technologies to deliver and measure ads, personalize content,
              and analyze traffic. Consent is required in the EEA, UK, and Switzerland. You can change your choices at any time.
              See our <a className="underline" href="/privacy">Privacy Policy</a>.
            </p>
            <div className="grid grid-cols-2 sm:grid-cols-4 gap-3 mt-3 text-xs">
              <label className="flex items-center gap-2"><Checkbox checked={prefs.ad_storage==='granted'} onCheckedChange={(v)=>setPrefs(p=>({...p, ad_storage: v? 'granted':'denied'}))}/> Ad storage</label>
              <label className="flex items-center gap-2"><Checkbox checked={prefs.ad_user_data==='granted'} onCheckedChange={(v)=>setPrefs(p=>({...p, ad_user_data: v? 'granted':'denied'}))}/> Ad user data</label>
              <label className="flex items-center gap-2"><Checkbox checked={prefs.ad_personalization==='granted'} onCheckedChange={(v)=>setPrefs(p=>({...p, ad_personalization: v? 'granted':'denied'}))}/> Ad personalization</label>
              <label className="flex items-center gap-2"><Checkbox checked={prefs.analytics_storage==='granted'} onCheckedChange={(v)=>setPrefs(p=>({...p, analytics_storage: v? 'granted':'denied'}))}/> Analytics</label>
            </div>
          </div>
          <div className="flex items-center gap-2 shrink-0">
            <Button variant="outline" onClick={rejectAll}>Reject all</Button>
            <Button variant="outline" onClick={saveChoices}>Save choices</Button>
            <Button onClick={acceptAll}>Accept all</Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ConsentBanner;
