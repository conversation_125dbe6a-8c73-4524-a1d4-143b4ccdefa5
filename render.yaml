services:
  - type: web
    name: python-ml-service
    env: docker
    plan: free
    dockerfilePath: python_service/Dockerfile
    autoDeploy: true
    healthCheckPath: /health
    envVars:
      - key: SUPABASE_URL
        sync: false
      - key: SUPABASE_SERVICE_ROLE_KEY
        sync: false
      - key: <PERSON><PERSON><PERSON><PERSON><PERSON>I_KEY
        sync: false
      - key: REQUEST_TIMEOUT_SECONDS
        value: "20"
      - key: USER_AGENT
        value: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
