import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Target, TrendingUp, Users, Clock, DollarSign, BarChart3 } from "lucide-react";

export type Category = {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
  color: string;
  markets: string[];
};

export const categories: Category[] = [
  {
    id: 'match-result',
    name: 'Match Result',
    description: 'Win/Draw/Lose predictions',
    icon: <Target className="h-4 w-4" />,
    color: 'primary',
    markets: ['1X2', 'Double Chance', 'Draw No Bet']
  },
  {
    id: 'goals-points',
    name: 'Goals & Points',
    description: 'Scoring predictions',
    icon: <TrendingUp className="h-4 w-4" />,
    color: 'success',
    markets: ['Over/Under Goals', 'Both Teams Score', 'Total Points', 'Handicap']
  },
  {
    id: 'player-props',
    name: 'Player Props',
    description: 'Individual player performance',
    icon: <Users className="h-4 w-4" />,
    color: 'info',
    markets: ['Goalscorer', 'Assists', 'Cards', 'Shots', 'Rebounds']
  },
  {
    id: 'halftime',
    name: 'Half Time',
    description: 'First half predictions',
    icon: <Clock className="h-4 w-4" />,
    color: 'warning',
    markets: ['HT Result', 'HT Over/Under', 'HT Asian Handicap']
  },
  {
    id: 'specials',
    name: 'Specials',
    description: 'Unique market opportunities',
    icon: <DollarSign className="h-4 w-4" />,
    color: 'destructive',
    markets: ['Corners', 'Bookings', 'Method of Victory', 'Exact Score']
  },
  {
    id: 'live',
    name: 'Live Betting',
    description: 'In-play predictions',
    icon: <BarChart3 className="h-4 w-4" />,
    color: 'accent',
    markets: ['Live 1X2', 'Next Goal', 'Live O/U', 'Live Handicap']
  }
];

interface CategorySelectorProps {
  selectedCategory?: string;
  onCategoryChange: (category: string) => void;
  showBadges?: boolean;
}

export const CategorySelector = ({ selectedCategory, onCategoryChange, showBadges = false }: CategorySelectorProps) => {
  if (showBadges) {
    return (
      <div className="flex flex-wrap gap-2">
        <Badge 
          variant={!selectedCategory || selectedCategory === 'all' ? "default" : "secondary"}
          className="cursor-pointer"
          onClick={() => onCategoryChange('all')}
        >
          All Categories
        </Badge>
        {categories.map((category) => (
          <Badge
            key={category.id}
            variant={selectedCategory === category.id ? "default" : "secondary"}
            className="cursor-pointer flex items-center gap-1"
            onClick={() => onCategoryChange(category.id)}
          >
            {category.icon}
            {category.name}
          </Badge>
        ))}
      </div>
    );
  }

  return (
    <Select value={selectedCategory || 'all'} onValueChange={onCategoryChange}>
      <SelectTrigger className="w-full">
        <SelectValue placeholder="Select category" />
      </SelectTrigger>
      <SelectContent>
        <SelectItem value="all">All Categories</SelectItem>
        {categories.map((category) => (
          <SelectItem key={category.id} value={category.id}>
            <div className="flex items-center gap-2">
              {category.icon}
              <div>
                <div>{category.name}</div>
                <div className="text-xs text-muted-foreground">{category.description}</div>
              </div>
            </div>
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
};