from typing import Dict, Any, Optional
from playwright.sync_api import sync_playwright
from app.scrapers.base import ScrapeRequest, backoff_sleep


def scrape_dynamic(req: ScrapeRequest) -> Dict[str, Any]:
    last_error: Optional[Exception] = None
    for attempt in range(4):
        try:
            with sync_playwright() as p:
                browser = p.chromium.launch(headless=True)
                context_args = {}
                if req.proxy_url:
                    context_args["proxy"] = {"server": req.proxy_url}
                context = browser.new_context(**context_args)
                page = context.new_page()
                if req.headers:
                    page.set_extra_http_headers(req.headers)
                page.goto(req.url, timeout=req.timeout * 1000)
                page.wait_for_timeout(req.wait_ms)
                title = page.title()
                content = page.content()
                browser.close()
                return {
                    "success": True,
                    "data": {
                        "title": title,
                        "html": content,
                    },
                    "metadata": {
                        "url": req.url,
                        "engine": "playwright",
                    },
                }
        except Exception as exc:
            last_error = exc
            if attempt == 3:
                break
            backoff_sleep(attempt)
    return {"success": False, "error": str(last_error) if last_error else "Unknown error"}
