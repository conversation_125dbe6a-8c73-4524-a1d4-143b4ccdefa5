import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Brain, Play, Pause, RefreshCw, Plus, Edit, Trash2, BarChart3, Zap, Target } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { apiService as api } from '@/services/api';
import { Header } from '@/components/Header';

interface MLModel {
  id: string;
  name: string;
  type: 'CLASSIFICATION' | 'REGRESSION' | 'NEURAL_NETWORK' | 'ENSEMBLE';
  sport: 'FOOTBALL' | 'BASKETBALL' | 'TENNIS' | 'BASEBALL' | 'ALL';
  status: 'TRAINING' | 'ACTIVE' | 'PAUSED' | 'FAILED' | 'DRAFT';
  accuracy: number;
  precision: number;
  recall: number;
  f1Score: number;
  trainingProgress: number;
  lastTrained: string;
  predictions: number;
  version: string;
  createdAt: string;
}

const AdminModels: React.FC = () => {
  const { toast } = useToast();
  const [models, setModels] = useState<MLModel[]>([]);
  const [loading, setLoading] = useState(true);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [selectedModel, setSelectedModel] = useState<MLModel | null>(null);
  const [trainingInProgress, setTrainingInProgress] = useState(false);

  const fetchModels = async () => {
    try {
      setLoading(true);
      const response = await api.getMLModels();
      if (response.success) {
        setModels(response.data || []);
      } else {
        // Mock data for development
        setModels([
          {
            id: '1',
            name: 'Football Outcome Predictor',
            type: 'NEURAL_NETWORK',
            sport: 'FOOTBALL',
            status: 'ACTIVE',
            accuracy: 87.5,
            precision: 85.2,
            recall: 89.1,
            f1Score: 87.1,
            trainingProgress: 100,
            lastTrained: '2024-01-15',
            predictions: 1250,
            version: 'v2.1',
            createdAt: '2024-01-01'
          },
          {
            id: '2',
            name: 'Basketball Score Predictor',
            type: 'REGRESSION',
            sport: 'BASKETBALL',
            status: 'TRAINING',
            accuracy: 82.3,
            precision: 80.1,
            recall: 84.5,
            f1Score: 82.2,
            trainingProgress: 65,
            lastTrained: '2024-01-10',
            predictions: 890,
            version: 'v1.8',
            createdAt: '2023-12-15'
          },
          {
            id: '3',
            name: 'Multi-Sport Ensemble',
            type: 'ENSEMBLE',
            sport: 'ALL',
            status: 'ACTIVE',
            accuracy: 91.2,
            precision: 89.8,
            recall: 92.5,
            f1Score: 91.1,
            trainingProgress: 100,
            lastTrained: '2024-01-20',
            predictions: 2340,
            version: 'v3.0',
            createdAt: '2024-01-05'
          }
        ]);
      }
    } catch (error) {
      console.error('Error fetching models:', error);
      toast({
        title: "Error",
        description: "Failed to fetch ML models",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchModels();
  }, []);

  const getStatusBadge = (status: string) => {
    const variants = {
      TRAINING: 'outline',
      ACTIVE: 'default',
      PAUSED: 'secondary',
      FAILED: 'destructive',
      DRAFT: 'outline'
    };
    return <Badge variant={variants[status as keyof typeof variants] as any}>{status}</Badge>;
  };

  const getTypeBadge = (type: string) => {
    const variants = {
      CLASSIFICATION: 'default',
      REGRESSION: 'secondary',
      NEURAL_NETWORK: 'outline',
      ENSEMBLE: 'destructive'
    };
    return <Badge variant={variants[type as keyof typeof variants] as any}>{type}</Badge>;
  };

  const handleStartTraining = async (modelId: string) => {
    try {
      setTrainingInProgress(true);
      const response = await api.startModelTraining(modelId);
      if (response.success) {
        toast({
          title: "Success",
          description: "Model training started successfully",
        });
        fetchModels();
      } else {
        throw new Error(response.error);
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to start model training",
        variant: "destructive",
      });
    } finally {
      setTrainingInProgress(false);
    }
  };

  const handleToggleModel = async (modelId: string, currentStatus: string) => {
    try {
      const newStatus = currentStatus === 'ACTIVE' ? 'PAUSED' : 'ACTIVE';
      const response = await api.updateModelStatus(modelId, newStatus);
      if (response.success) {
        toast({
          title: "Success",
          description: `Model ${newStatus.toLowerCase()} successfully`,
        });
        fetchModels();
      } else {
        throw new Error(response.error);
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update model status",
        variant: "destructive",
      });
    }
  };

  const stats = {
    totalModels: models.length,
    activeModels: models.filter(m => m.status === 'ACTIVE').length,
    avgAccuracy: models.length > 0 ? models.reduce((sum, m) => sum + m.accuracy, 0) / models.length : 0,
    totalPredictions: models.reduce((sum, m) => sum + m.predictions, 0),
    trainingModels: models.filter(m => m.status === 'TRAINING').length
  };

  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2">ML Model Management</h1>
          <p className="text-muted-foreground">Manage and monitor machine learning models</p>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-5 gap-6 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Models</CardTitle>
              <Brain className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.totalModels}</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Models</CardTitle>
              <Zap className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.activeModels}</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Avg Accuracy</CardTitle>
              <Target className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.avgAccuracy.toFixed(1)}%</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Predictions Made</CardTitle>
              <BarChart3 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.totalPredictions.toLocaleString()}</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Training</CardTitle>
              <RefreshCw className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.trainingModels}</div>
            </CardContent>
          </Card>
        </div>

        <Tabs defaultValue="models" className="space-y-6">
          <TabsList>
            <TabsTrigger value="models">Models</TabsTrigger>
            <TabsTrigger value="training">Training</TabsTrigger>
            <TabsTrigger value="performance">Performance</TabsTrigger>
          </TabsList>

          <TabsContent value="models">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle>ML Models</CardTitle>
                    <CardDescription>Manage your machine learning models</CardDescription>
                  </div>
                  <div className="flex items-center gap-2">
                    <Button onClick={fetchModels} variant="outline" size="sm">
                      <RefreshCw className="h-4 w-4 mr-2" />
                      Refresh
                    </Button>
                    <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
                      <DialogTrigger asChild>
                        <Button size="sm">
                          <Plus className="h-4 w-4 mr-2" />
                          Create Model
                        </Button>
                      </DialogTrigger>
                      <DialogContent>
                        <DialogHeader>
                          <DialogTitle>Create New ML Model</DialogTitle>
                          <DialogDescription>Configure a new machine learning model</DialogDescription>
                        </DialogHeader>
                        <div className="space-y-4">
                          <div>
                            <Label htmlFor="modelName">Model Name</Label>
                            <Input id="modelName" placeholder="Football Outcome Predictor" />
                          </div>
                          <div>
                            <Label htmlFor="modelType">Model Type</Label>
                            <Select>
                              <SelectTrigger>
                                <SelectValue placeholder="Select type" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="CLASSIFICATION">Classification</SelectItem>
                                <SelectItem value="REGRESSION">Regression</SelectItem>
                                <SelectItem value="NEURAL_NETWORK">Neural Network</SelectItem>
                                <SelectItem value="ENSEMBLE">Ensemble</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                          <div>
                            <Label htmlFor="sport">Sport</Label>
                            <Select>
                              <SelectTrigger>
                                <SelectValue placeholder="Select sport" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="FOOTBALL">Football</SelectItem>
                                <SelectItem value="BASKETBALL">Basketball</SelectItem>
                                <SelectItem value="TENNIS">Tennis</SelectItem>
                                <SelectItem value="BASEBALL">Baseball</SelectItem>
                                <SelectItem value="ALL">All Sports</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                          <div>
                            <Label htmlFor="description">Description</Label>
                            <Textarea id="description" placeholder="Model description..." />
                          </div>
                          <Button className="w-full">Create Model</Button>
                        </div>
                      </DialogContent>
                    </Dialog>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Name</TableHead>
                      <TableHead>Type</TableHead>
                      <TableHead>Sport</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Accuracy</TableHead>
                      <TableHead>F1 Score</TableHead>
                      <TableHead>Predictions</TableHead>
                      <TableHead>Version</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {loading ? (
                      <TableRow>
                        <TableCell colSpan={9} className="text-center py-8">
                          Loading models...
                        </TableCell>
                      </TableRow>
                    ) : models.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={9} className="text-center py-8">
                          No models found
                        </TableCell>
                      </TableRow>
                    ) : (
                      models.map((model) => (
                        <TableRow key={model.id}>
                          <TableCell className="font-medium">{model.name}</TableCell>
                          <TableCell>{getTypeBadge(model.type)}</TableCell>
                          <TableCell>{model.sport}</TableCell>
                          <TableCell>
                            <div className="flex items-center gap-2">
                              {getStatusBadge(model.status)}
                              {model.status === 'TRAINING' && (
                                <Progress value={model.trainingProgress} className="w-16" />
                              )}
                            </div>
                          </TableCell>
                          <TableCell>{model.accuracy.toFixed(1)}%</TableCell>
                          <TableCell>{model.f1Score.toFixed(1)}%</TableCell>
                          <TableCell>{model.predictions.toLocaleString()}</TableCell>
                          <TableCell>{model.version}</TableCell>
                          <TableCell>
                            <div className="flex items-center gap-2">
                              <Button 
                                variant="outline" 
                                size="sm"
                                onClick={() => handleStartTraining(model.id)}
                                disabled={model.status === 'TRAINING' || trainingInProgress}
                              >
                                <RefreshCw className="h-4 w-4" />
                              </Button>
                              <Button 
                                variant="outline" 
                                size="sm"
                                onClick={() => handleToggleModel(model.id, model.status)}
                                disabled={model.status === 'TRAINING'}
                              >
                                {model.status === 'ACTIVE' ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
                              </Button>
                              <Button variant="outline" size="sm">
                                <Edit className="h-4 w-4" />
                              </Button>
                              <Button variant="outline" size="sm">
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="training">
            <Card>
              <CardHeader>
                <CardTitle>Model Training</CardTitle>
                <CardDescription>Monitor and manage model training processes</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {models.filter(m => m.status === 'TRAINING').map((model) => (
                    <div key={model.id} className="border rounded-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <h3 className="font-medium">{model.name}</h3>
                        <Badge>{model.status}</Badge>
                      </div>
                      <Progress value={model.trainingProgress} className="mb-2" />
                      <p className="text-sm text-muted-foreground">
                        Training Progress: {model.trainingProgress}%
                      </p>
                    </div>
                  ))}
                  {models.filter(m => m.status === 'TRAINING').length === 0 && (
                    <div className="text-center py-8 text-muted-foreground">
                      No models currently training
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="performance">
            <Card>
              <CardHeader>
                <CardTitle>Model Performance</CardTitle>
                <CardDescription>Detailed performance metrics for your models</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8 text-muted-foreground">
                  Performance analytics dashboard coming soon...
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default AdminModels;
