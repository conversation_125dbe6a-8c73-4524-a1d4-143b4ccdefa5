import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Trophy, Clock, TrendingUp, Target } from "lucide-react";
import { cn } from "@/lib/utils";

interface PredictionCardProps {
  match: {
    id: string;
    homeTeam: string;
    awayTeam: string;
    league: string;
    sport: string;
    startTime: string;
    market: string;
    prediction: string;
    confidence: number;
    odds: number;
    edge: number;
    source: 'ML' | 'MANUAL';
  };
}

export const PredictionCard = ({ match }: PredictionCardProps) => {
  const confidenceColor = match.confidence >= 75 
    ? 'confidence-high' 
    : match.confidence >= 60 
      ? 'confidence-medium' 
      : 'confidence-low';

  const sportColor = {
    'Football': 'sports-football',
    'Basketball': 'sports-basketball', 
    'Tennis': 'sports-tennis',
    'Baseball': 'sports-baseball'
  }[match.sport] || 'muted';

  return (
    <Card className="p-4 hover:shadow-lg transition-all duration-300 bg-gradient-card border border-border/50 group">
      {/* Header */}
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center space-x-2">
          <div className={cn("w-2 h-2 rounded-full", `bg-${sportColor}`)} />
          <span className="text-xs font-medium text-muted-foreground uppercase tracking-wide">
            {match.league}
          </span>
        </div>
        <Badge variant={match.source === 'ML' ? 'secondary' : 'default'} className="text-xs">
          {match.source === 'ML' ? 'AI Model' : 'Expert'}
        </Badge>
      </div>

      {/* Match Info */}
      <div className="space-y-2 mb-4">
        <div className="flex items-center justify-between flex-wrap gap-2">
          <h3 className="font-semibold text-foreground group-hover:text-primary transition-colors text-sm sm:text-base break-words">
            {match.homeTeam} vs {match.awayTeam}
          </h3>
          <div className="flex items-center text-xs sm:text-sm text-muted-foreground flex-shrink-0">
            <Clock className="w-3 h-3 sm:w-4 sm:h-4 mr-1" />
            <span className="break-words">{match.startTime}</span>
          </div>
        </div>
        
        <div className="text-xs sm:text-sm text-muted-foreground break-words">
          {match.market}: <span className="font-medium text-foreground">{match.prediction}</span>
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-3 gap-2 sm:gap-3">
        <div className="text-center">
          <div className="flex items-center justify-center mb-1">
            <Target className="w-3 h-3 text-muted-foreground mr-1" />
          </div>
          <div className={cn("text-xs sm:text-sm font-semibold", `text-${confidenceColor}`)}>
            {match.confidence}%
          </div>
          <div className="text-xs text-muted-foreground hidden sm:block">Confidence</div>
          <div className="text-xs text-muted-foreground sm:hidden">Conf</div>
        </div>

        <div className="text-center">
          <div className="flex items-center justify-center mb-1">
            <Trophy className="w-3 h-3 text-muted-foreground mr-1" />
          </div>
          <div className="text-xs sm:text-sm font-semibold text-foreground">
            {match.odds.toFixed(2)}
          </div>
          <div className="text-xs text-muted-foreground">Odds</div>
        </div>

        <div className="text-center">
          <div className="flex items-center justify-center mb-1">
            <TrendingUp className="w-3 h-3 text-muted-foreground mr-1" />
          </div>
          <div className={cn("text-xs sm:text-sm font-semibold", match.edge > 0 ? 'text-success' : 'text-destructive')}>
            {match.edge > 0 ? '+' : ''}{match.edge.toFixed(1)}%
          </div>
          <div className="text-xs text-muted-foreground">Edge</div>
        </div>
      </div>
    </Card>
  );
};