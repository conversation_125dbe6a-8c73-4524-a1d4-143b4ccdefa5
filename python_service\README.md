# Python Service (Scraping + ML)

This service provides:

- FastAPI endpoints for scraping and ML predictions
- Two scraping engines:
  - Requests + BeautifulSoup for static pages
  - Playwright (headless Chromium) for JS-heavy pages
- Optional proxy support (HTTP/SOCKS)
- ML pipeline (baseline with scikit-learn; optional TensorFlow/Keras)
- Supabase integration using the service role to read/write data

## Endpoints

- GET /health – liveness check
- POST /scrape – scrape a target URL or provider
- POST /predict – run ML predictions given match payload or from DB
- POST /ingest – upsert scraped items into Supabase tables

## Quickstart (local)

1. Python 3.11+
2. Create virtual env and install deps

```bash
python -m venv .venv
. .venv/Scripts/activate  # Windows PowerShell: .venv\Scripts\Activate.ps1
pip install -r requirements.txt
# Install Playwright browsers
python -m playwright install chromium
```

3. Configure environment

Copy .env.example to .env and set values:

- SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY
- PROXY_URL (optional, supports *********************:port or socks5://...)
- SCRAPERAPI_KEY (optional; enables fallback via ScraperAPI when a request fails)

To test ScraperAPI directly:

```bash
# Python
python - << 'PY'
import requests
payload = {'api_key': '<YOUR_SCRAPERAPI_KEY>', 'url': 'https://httpbin.org/'}
print(requests.get('https://api.scraperapi.com/', params=payload).text)
PY

# cURL
curl "https://api.scraperapi.com/?api_key=<YOUR_SCRAPERAPI_KEY>&url=https%3A%2F%2Fhttpbin.org%2F"
```

4. Run the API

```bash
uvicorn app.main:app --reload --port 8088
```

## Deploy

- Containerize with the provided Dockerfile
- Host on Render/Fly.io/Railway/AWS; ensure network egress to targets and to Supabase

## Notes

- TensorFlow is optional and heavy; enabled if import succeeds. Model falls back to scikit-learn if TF unavailable.
- For large-scale scraping, prefer rotating proxies and backoff; see app/scrapers/base.py for hooks.
- This mirrors patterns from open-source projects like bettingAI (FastAPI + Keras + betting EV math).
