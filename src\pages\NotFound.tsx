import { useLocation } from "react-router-dom";
import { useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Home, Search, TrendingUp } from "lucide-react";

const NotFound = () => {
  const location = useLocation();

  useEffect(() => {
    console.error(
      "404 Error: User attempted to access non-existent route:",
      location.pathname
    );
  }, [location.pathname]);

  return (
    <div className="min-h-screen bg-background flex items-center justify-center">
      <Card className="max-w-md w-full mx-4 p-8 text-center bg-gradient-card border border-border/50">
        <div className="w-20 h-20 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-6">
          <Search className="w-10 h-10 text-primary" />
        </div>
        
        <h1 className="text-4xl font-bold text-foreground mb-2">404</h1>
        <h2 className="text-xl font-semibold text-foreground mb-4">Page Not Found</h2>
        
        <p className="text-muted-foreground mb-8">
          The predictions you're looking for have moved or don't exist. 
          Let's get you back to analyzing the winning picks!
        </p>
        
        <div className="space-y-3">
          <Button asChild className="w-full">
            <a href="/">
              <Home className="w-4 h-4 mr-2" />
              Back to Dashboard
            </a>
          </Button>
          
          <Button variant="outline" asChild className="w-full">
            <a href="/predictions">
              <TrendingUp className="w-4 h-4 mr-2" />
              View Predictions
            </a>
          </Button>
        </div>
        
        <div className="mt-8 pt-6 border-t border-border">
          <p className="text-sm text-muted-foreground">
            Looking for something specific? Try our{" "}
            <a href="/predictions" className="text-primary hover:underline">
              predictions page
            </a>{" "}
            or{" "}
            <a href="/" className="text-primary hover:underline">
              main dashboard
            </a>
          </p>
        </div>
      </Card>
    </div>
  );
};

export default NotFound;
