AGENTS guide for this repo (Vite React TS + Express backend + Supabase edge functions)

- Build/dev: frontend at root: `npm i && npm run dev` (Vite on 8080); build: `npm run build`; preview: `npm run preview`. Lint: `npm run lint`.
- Backend: in [backend/package.json](file:///c:/Users/<USER>/OneDrive/Documents/1300blk-77777/onethreezerozeroblk-77/backend/package.json), install then `npm run dev` (nodemon) or `npm start`. API served from [server.js](file:///c:/Users/<USER>/OneDrive/Documents/1300blk-77777/onethreezerozeroblk-77/backend/server.js).
- Tests: Backend uses Jest (`npm test` in backend). Single test: `npx jest path/to/file.test.js -t "test name"` (from backend dir). No frontend test setup.
- Architecture: Frontend (React/TS) in [src/](file:///c:/Users/<USER>/OneDrive/Documents/1300blk-77777/onethreezerozeroblk-77/src) uses alias `@/*` (see [vite.config.ts](file:///c:/Users/<USER>/OneDrive/Documents/1300blk-77777/onethreezerozeroblk-77/vite.config.ts), [tsconfig.json](file:///c:/Users/<USER>/OneDrive/Documents/1300blk-77777/onethreezerozeroblk-77/tsconfig.json)). API wrapper in [src/services/api.ts](file:///c:/Users/<USER>/OneDrive/Documents/1300blk-77777/onethreezerozeroblk-77/src/services/api.ts) calls Supabase functions/DB.
- Backend: Express routes in [backend/routes/](file:///c:/Users/<USER>/OneDrive/Documents/1300blk-77777/onethreezerozeroblk-77/backend/routes) (auth, predictions, sportsData, models). Business logic in [backend/services/](file:///c:/Users/<USER>/OneDrive/Documents/1300blk-77777/onethreezerozeroblk-77/backend/services).
- Supabase Edge Functions (Deno) in [supabase/functions/](file:///c:/Users/<USER>/OneDrive/Documents/1300blk-77777/onethreezerozeroblk-77/supabase/functions) (e.g., [scrape-sports-data](file:///c:/Users/<USER>/OneDrive/Documents/1300blk-77777/onethreezerozeroblk-77/supabase/functions/scrape-sports-data/index.ts)). They use `createClient` with service role and CORS headers.
- Data: Supabase Postgres tables referenced include `profiles`, `global_settings`, `sports_data`, `audit_logs`, `live_predictions`, `predictions`.
- External APIs: ESPN, The Odds API, SportsReference, SportsRadar consumed inside edge functions; RapidAPI placeholders in backend services.
- Code style: TypeScript in frontend (ES modules, React JSX), CommonJS in backend. Import alias `@` to `src/*`. Prefer explicit return types in TS; use `ApiResponse<T>` pattern with `{ success, data, error }`.
- Linting: ESLint via [eslint.config.js](file:///c:/Users/<USER>/OneDrive/Documents/1300blk-77777/onethreezerozeroblk-77/eslint.config.js) (typescript-eslint, react-hooks). No Prettier config; keep 2-space indent, semicolons, consistent quotes.
- Error handling: Wrap API/edge handlers in try/catch; log errors; return JSON with message; edge responses include CORS headers.
- Env/secrets: Backend uses `.env` (see [backend/.env.example](file:///c:/Users/<USER>/OneDrive/Documents/1300blk-77777/onethreezerozeroblk-77/backend/.env.example)); Supabase client keys stored in frontend ([supabase.ts](file:///c:/Users/<USER>/OneDrive/Documents/1300blk-77777/onethreezerozeroblk-77/src/lib/supabase.ts)) are redacted; never commit real secrets.
- Conventions: Use `@` paths in imports; group external/internal imports; name files in kebab/pascal consistently (`Component.tsx`, service classes in CamelCase). Avoid unused vars (ESLint rules allow some flexibility).
- Running edge functions locally: Use Supabase CLI (`supabase functions serve <name>`) with required env vars (`SUPABASE_URL`, `SUPABASE_SERVICE_ROLE_KEY`, API keys) set; functions are Deno (no Node APIs).
- No Cursor/Claude/Windsurf/Cline/Goose/Copilot rule files found. If added later, update this guide.
