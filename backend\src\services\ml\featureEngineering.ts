import { MatchData } from '@/services/scraping/baseScraper';
import { prisma } from '@/config/database';
import { logger } from '@/utils/logger';

export interface FeatureVector {
  // Team strength features
  home_rating: number;
  away_rating: number;
  home_offense_rating: number;
  away_offense_rating: number;
  home_defense_rating: number;
  away_defense_rating: number;
  
  // Head-to-head features
  h2h_home_wins: number;
  h2h_away_wins: number;
  h2h_total_games: number;
  h2h_avg_home_score: number;
  h2h_avg_away_score: number;
  
  // Recent form features
  home_recent_wins: number;
  away_recent_wins: number;
  home_recent_losses: number;
  away_recent_losses: number;
  home_recent_avg_score: number;
  away_recent_avg_score: number;
  home_recent_avg_allowed: number;
  away_recent_avg_allowed: number;
  
  // Situational features
  home_field_advantage: number;
  rest_days_home: number;
  rest_days_away: number;
  travel_distance: number;
  
  // Market features
  opening_line: number;
  line_movement: number;
  public_betting_percentage: number;
  sharp_money_indicator: number;
  
  // Weather features (for outdoor sports)
  temperature?: number;
  wind_speed?: number;
  precipitation?: number;
  
  // Injury features
  home_injury_impact: number;
  away_injury_impact: number;
  
  // Motivation features
  playoff_implications: number;
  rivalry_game: number;
  revenge_game: number;
}

export class FeatureEngineer {
  
  async extractFeatures(match: MatchData): Promise<FeatureVector> {
    try {
      const features: Partial<FeatureVector> = {};
      
      // Extract basic team ratings
      if (match.stats) {
        features.home_rating = match.stats.home_rating || 75;
        features.away_rating = match.stats.away_rating || 75;
        features.home_offense_rating = match.stats.home_offense_rating || features.home_rating;
        features.away_offense_rating = match.stats.away_offense_rating || features.away_rating;
        features.home_defense_rating = match.stats.home_defense_rating || features.home_rating;
        features.away_defense_rating = match.stats.away_defense_rating || features.away_rating;
      } else {
        // Generate default ratings
        features.home_rating = this.generateTeamRating(match.homeTeam);
        features.away_rating = this.generateTeamRating(match.awayTeam);
        features.home_offense_rating = features.home_rating;
        features.away_offense_rating = features.away_rating;
        features.home_defense_rating = features.home_rating;
        features.away_defense_rating = features.away_rating;
      }
      
      // Extract head-to-head features
      const h2hFeatures = await this.extractHeadToHeadFeatures(match.homeTeam, match.awayTeam, match.sport, match.league);
      Object.assign(features, h2hFeatures);
      
      // Extract recent form features
      const formFeatures = await this.extractRecentFormFeatures(match.homeTeam, match.awayTeam, match.sport, match.league, match.matchDate);
      Object.assign(features, formFeatures);
      
      // Extract situational features
      const situationalFeatures = this.extractSituationalFeatures(match);
      Object.assign(features, situationalFeatures);
      
      // Extract market features
      const marketFeatures = this.extractMarketFeatures(match);
      Object.assign(features, marketFeatures);
      
      // Extract injury features
      const injuryFeatures = this.extractInjuryFeatures(match);
      Object.assign(features, injuryFeatures);
      
      // Extract motivation features
      const motivationFeatures = this.extractMotivationFeatures(match);
      Object.assign(features, motivationFeatures);
      
      return this.fillMissingFeatures(features as FeatureVector);
      
    } catch (error) {
      logger.error(`Feature extraction failed for match ${match.matchId}: ${error}`);
      return this.getDefaultFeatures();
    }
  }
  
  private async extractHeadToHeadFeatures(homeTeam: string, awayTeam: string, sport: string, league: string): Promise<Partial<FeatureVector>> {
    try {
      // Get historical matchups between these teams
      const h2hMatches = await prisma.sportsData.findMany({
        where: {
          sport,
          league,
          OR: [
            { homeTeam, awayTeam },
            { homeTeam: awayTeam, awayTeam: homeTeam },
          ],
          status: 'COMPLETED',
          homeScore: { not: null },
          awayScore: { not: null },
        },
        orderBy: { matchDate: 'desc' },
        take: 10, // Last 10 meetings
      });
      
      if (h2hMatches.length === 0) {
        return {
          h2h_home_wins: 0,
          h2h_away_wins: 0,
          h2h_total_games: 0,
          h2h_avg_home_score: 0,
          h2h_avg_away_score: 0,
        };
      }
      
      let homeWins = 0;
      let awayWins = 0;
      let totalHomeScore = 0;
      let totalAwayScore = 0;
      
      for (const match of h2hMatches) {
        if (match.homeTeam === homeTeam) {
          // Current home team was home in this historical match
          if ((match.homeScore || 0) > (match.awayScore || 0)) {
            homeWins++;
          } else {
            awayWins++;
          }
          totalHomeScore += match.homeScore || 0;
          totalAwayScore += match.awayScore || 0;
        } else {
          // Current home team was away in this historical match
          if ((match.awayScore || 0) > (match.homeScore || 0)) {
            homeWins++;
          } else {
            awayWins++;
          }
          totalHomeScore += match.awayScore || 0;
          totalAwayScore += match.homeScore || 0;
        }
      }
      
      return {
        h2h_home_wins: homeWins,
        h2h_away_wins: awayWins,
        h2h_total_games: h2hMatches.length,
        h2h_avg_home_score: totalHomeScore / h2hMatches.length,
        h2h_avg_away_score: totalAwayScore / h2hMatches.length,
      };
      
    } catch (error) {
      logger.error(`H2H feature extraction failed: ${error}`);
      return {
        h2h_home_wins: 0,
        h2h_away_wins: 0,
        h2h_total_games: 0,
        h2h_avg_home_score: 0,
        h2h_avg_away_score: 0,
      };
    }
  }
  
  private async extractRecentFormFeatures(homeTeam: string, awayTeam: string, sport: string, league: string, matchDate: Date): Promise<Partial<FeatureVector>> {
    try {
      const cutoffDate = new Date(matchDate);
      cutoffDate.setDate(cutoffDate.getDate() - 30); // Last 30 days
      
      // Get recent games for home team
      const homeRecentGames = await prisma.sportsData.findMany({
        where: {
          sport,
          league,
          OR: [
            { homeTeam },
            { awayTeam: homeTeam },
          ],
          status: 'COMPLETED',
          matchDate: { gte: cutoffDate, lt: matchDate },
          homeScore: { not: null },
          awayScore: { not: null },
        },
        orderBy: { matchDate: 'desc' },
        take: 10,
      });
      
      // Get recent games for away team
      const awayRecentGames = await prisma.sportsData.findMany({
        where: {
          sport,
          league,
          OR: [
            { homeTeam: awayTeam },
            { awayTeam },
          ],
          status: 'COMPLETED',
          matchDate: { gte: cutoffDate, lt: matchDate },
          homeScore: { not: null },
          awayScore: { not: null },
        },
        orderBy: { matchDate: 'desc' },
        take: 10,
      });
      
      const homeForm = this.calculateTeamForm(homeRecentGames, homeTeam);
      const awayForm = this.calculateTeamForm(awayRecentGames, awayTeam);
      
      return {
        home_recent_wins: homeForm.wins,
        away_recent_wins: awayForm.wins,
        home_recent_losses: homeForm.losses,
        away_recent_losses: awayForm.losses,
        home_recent_avg_score: homeForm.avgScore,
        away_recent_avg_score: awayForm.avgScore,
        home_recent_avg_allowed: homeForm.avgAllowed,
        away_recent_avg_allowed: awayForm.avgAllowed,
      };
      
    } catch (error) {
      logger.error(`Recent form feature extraction failed: ${error}`);
      return {
        home_recent_wins: 0,
        away_recent_wins: 0,
        home_recent_losses: 0,
        away_recent_losses: 0,
        home_recent_avg_score: 0,
        away_recent_avg_score: 0,
        home_recent_avg_allowed: 0,
        away_recent_avg_allowed: 0,
      };
    }
  }
  
  private calculateTeamForm(games: any[], teamName: string) {
    let wins = 0;
    let losses = 0;
    let totalScore = 0;
    let totalAllowed = 0;
    
    for (const game of games) {
      const isHome = game.homeTeam === teamName;
      const teamScore = isHome ? (game.homeScore || 0) : (game.awayScore || 0);
      const opponentScore = isHome ? (game.awayScore || 0) : (game.homeScore || 0);
      
      if (teamScore > opponentScore) {
        wins++;
      } else {
        losses++;
      }
      
      totalScore += teamScore;
      totalAllowed += opponentScore;
    }
    
    return {
      wins,
      losses,
      avgScore: games.length > 0 ? totalScore / games.length : 0,
      avgAllowed: games.length > 0 ? totalAllowed / games.length : 0,
    };
  }
  
  private extractSituationalFeatures(match: MatchData): Partial<FeatureVector> {
    return {
      home_field_advantage: this.calculateHomeFieldAdvantage(match.sport, match.league),
      rest_days_home: 3, // Default - would need schedule data
      rest_days_away: 3, // Default - would need schedule data
      travel_distance: this.estimateTravelDistance(match.homeTeam, match.awayTeam),
    };
  }
  
  private extractMarketFeatures(match: MatchData): Partial<FeatureVector> {
    const odds = match.odds;
    let openingLine = 0;
    
    if (odds?.spread?.line) {
      openingLine = Math.abs(odds.spread.line);
    } else if (odds?.moneyline) {
      // Convert moneyline to implied spread
      const homeImplied = this.moneylineToImpliedProbability(odds.moneyline.home);
      const awayImplied = this.moneylineToImpliedProbability(odds.moneyline.away);
      openingLine = Math.abs(homeImplied - awayImplied) * 20; // Rough conversion
    }
    
    return {
      opening_line: openingLine,
      line_movement: 0, // Would need historical line data
      public_betting_percentage: 50, // Default - would need betting data
      sharp_money_indicator: 0, // Would need sharp betting data
    };
  }
  
  private extractInjuryFeatures(match: MatchData): Partial<FeatureVector> {
    // In a real implementation, this would analyze injury reports
    return {
      home_injury_impact: 0,
      away_injury_impact: 0,
    };
  }
  
  private extractMotivationFeatures(match: MatchData): Partial<FeatureVector> {
    return {
      playoff_implications: this.assessPlayoffImplications(match),
      rivalry_game: this.isRivalryGame(match.homeTeam, match.awayTeam) ? 1 : 0,
      revenge_game: 0, // Would need historical context
    };
  }
  
  private generateTeamRating(teamName: string): number {
    // Simple hash-based rating for demo
    let hash = 0;
    for (let i = 0; i < teamName.length; i++) {
      const char = teamName.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash;
    }
    return Math.abs(hash % 30) + 70; // 70-100 range
  }
  
  private calculateHomeFieldAdvantage(sport: string, league: string): number {
    const advantages: { [key: string]: number } = {
      'football': 3.0,
      'basketball': 4.0,
      'baseball': 1.5,
      'hockey': 2.0,
      'soccer': 2.5,
    };
    return advantages[sport.toLowerCase()] || 2.0;
  }
  
  private estimateTravelDistance(homeTeam: string, awayTeam: string): number {
    // Simplified distance estimation based on team names
    // In reality, would use actual city coordinates
    return Math.random() * 2000; // 0-2000 miles
  }
  
  private moneylineToImpliedProbability(moneyline: number): number {
    if (moneyline > 0) {
      return 100 / (moneyline + 100);
    } else {
      return Math.abs(moneyline) / (Math.abs(moneyline) + 100);
    }
  }
  
  private assessPlayoffImplications(match: MatchData): number {
    // Simplified - would need standings and schedule data
    const lateSeasonMonths = [11, 12, 1, 2, 3]; // Nov-Mar
    const month = match.matchDate.getMonth() + 1;
    return lateSeasonMonths.includes(month) ? 1 : 0;
  }
  
  private isRivalryGame(homeTeam: string, awayTeam: string): boolean {
    // Simplified rivalry detection
    const rivalries = [
      ['Lakers', 'Celtics'],
      ['Yankees', 'Red Sox'],
      ['Cowboys', 'Giants'],
      // Add more rivalries
    ];
    
    return rivalries.some(rivalry => 
      (homeTeam.includes(rivalry[0]) && awayTeam.includes(rivalry[1])) ||
      (homeTeam.includes(rivalry[1]) && awayTeam.includes(rivalry[0]))
    );
  }
  
  private fillMissingFeatures(features: FeatureVector): FeatureVector {
    const defaults = this.getDefaultFeatures();
    return { ...defaults, ...features };
  }
  
  private getDefaultFeatures(): FeatureVector {
    return {
      home_rating: 75,
      away_rating: 75,
      home_offense_rating: 75,
      away_offense_rating: 75,
      home_defense_rating: 75,
      away_defense_rating: 75,
      h2h_home_wins: 0,
      h2h_away_wins: 0,
      h2h_total_games: 0,
      h2h_avg_home_score: 0,
      h2h_avg_away_score: 0,
      home_recent_wins: 0,
      away_recent_wins: 0,
      home_recent_losses: 0,
      away_recent_losses: 0,
      home_recent_avg_score: 0,
      away_recent_avg_score: 0,
      home_recent_avg_allowed: 0,
      away_recent_avg_allowed: 0,
      home_field_advantage: 2.0,
      rest_days_home: 3,
      rest_days_away: 3,
      travel_distance: 500,
      opening_line: 0,
      line_movement: 0,
      public_betting_percentage: 50,
      sharp_money_indicator: 0,
      home_injury_impact: 0,
      away_injury_impact: 0,
      playoff_implications: 0,
      rivalry_game: 0,
      revenge_game: 0,
    };
  }
}
