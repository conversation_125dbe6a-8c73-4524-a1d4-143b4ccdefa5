import { useState, useEffect } from 'react';

export interface PreloaderConfig {
  minDuration?: number;
  maxDuration?: number;
  enableDeviceDetection?: boolean;
  customSteps?: Array<{
    id: string;
    label: string;
    duration: number;
  }>;
}

export const usePreloader = (config: PreloaderConfig = {}) => {
  const [isLoading, setIsLoading] = useState(true);
  const [currentStep, setCurrentStep] = useState('');
  const [progress, setProgress] = useState(0);

  const {
    minDuration = 2000,
    maxDuration = 4000,
    enableDeviceDetection = true,
    customSteps
  } = config;

  useEffect(() => {
    let mounted = true;

    const runPreloader = async () => {
      if (!mounted) return;

      // Device-specific loading times
      const getDeviceLoadTime = () => {
        if (!enableDeviceDetection) return minDuration;

        const connection = (navigator as any).connection;
        const isSlowConnection = connection && connection.effectiveType && 
          ['slow-2g', '2g', '3g'].includes(connection.effectiveType);
        
        const isLowEndDevice = navigator.hardwareConcurrency && navigator.hardwareConcurrency <= 2;
        
        if (isSlowConnection || isLowEndDevice) {
          return maxDuration;
        } else {
          return minDuration;
        }
      };

      const loadTime = getDeviceLoadTime();
      const startTime = Date.now();

      // Default steps if none provided
      const steps = customSteps || [
        { id: 'init', label: 'Initializing Application', duration: 0.2 },
        { id: 'auth', label: 'Verifying Authentication', duration: 0.3 },
        { id: 'data', label: 'Loading Sports Data', duration: 0.3 },
        { id: 'ui', label: 'Preparing Interface', duration: 0.15 },
        { id: 'complete', label: 'Ready to Launch', duration: 0.05 }
      ];

      for (let i = 0; i < steps.length; i++) {
        if (!mounted) return;

        const step = steps[i];
        setCurrentStep(step.label);
        
        const stepDuration = step.duration * loadTime;
        const stepStartTime = Date.now();
        
        // Animate progress for this step
        const animateStep = () => {
          if (!mounted) return;
          
          const elapsed = Date.now() - stepStartTime;
          const stepProgress = Math.min(elapsed / stepDuration, 1);
          const totalProgress = ((i + stepProgress) / steps.length) * 100;
          
          setProgress(totalProgress);
          
          if (stepProgress < 1) {
            requestAnimationFrame(animateStep);
          }
        };
        
        animateStep();
        await new Promise(resolve => setTimeout(resolve, stepDuration));
      }

      // Ensure minimum load time
      const totalElapsed = Date.now() - startTime;
      if (totalElapsed < minDuration) {
        await new Promise(resolve => setTimeout(resolve, minDuration - totalElapsed));
      }

      if (mounted) {
        setProgress(100);
        setTimeout(() => {
          if (mounted) setIsLoading(false);
        }, 300);
      }
    };

    runPreloader();

    return () => {
      mounted = false;
    };
  }, [minDuration, maxDuration, enableDeviceDetection, customSteps]);

  return {
    isLoading,
    currentStep,
    progress,
    setIsLoading
  };
};