import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { apiService } from '@/services/api';
import { format } from 'date-fns';
import { TrendingUp, Clock, Zap, Play, Pause, RefreshCw } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface LivePrediction {
  id: string;
  sport: string;
  league: string;
  home_team: string;
  away_team: string;
  match_date: string;
  market: string;
  pick: string;
  confidence: number;
  odds: number;
  edge?: number;
  expected_value?: number;
  factors?: any;
  source: string;
  status: string;
  created_at: string;
  is_premium?: boolean;
}

interface RealTimePredictionsProps {
  showControls?: boolean;
  maxPredictions?: number;
}

export const RealTimePredictions: React.FC<RealTimePredictionsProps> = ({ 
  showControls = true, 
  maxPredictions = 10 
}) => {
  const [predictions, setPredictions] = useState<LivePrediction[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isRealTimeActive, setIsRealTimeActive] = useState(true);
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null);
  const { toast } = useToast();

  // Load predictions from API
  useEffect(() => {
    loadPredictions();
  }, [maxPredictions]);

  // Polling for real-time updates (since we don't have WebSocket yet)
  useEffect(() => {
    if (!isRealTimeActive) return;

    const interval = setInterval(() => {
      loadPredictions();
    }, 30000); // Poll every 30 seconds

    return () => {
      clearInterval(interval);
    };
  }, [isRealTimeActive, maxPredictions]);

  const loadPredictions = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await apiService.getAllPredictions();

      if (!response.success || !response.data) {
        throw new Error(`API error: ${response.error}`);
      }

      // Map PredictionData to LivePrediction format
      const livePredictions = response.data.predictions.map(pred => ({
        id: pred.id,
        sport: pred.sport,
        league: pred.league,
        home_team: pred.homeTeam,
        away_team: pred.awayTeam,
        match_date: pred.matchDate,
        market: pred.market,
        pick: pred.pick,
        confidence: pred.confidence,
        odds: pred.odds,
        edge: pred.expectedValue,
        expected_value: pred.expectedValue,
        factors: pred.factors,
        source: 'API',
        status: 'upcoming',
        created_at: pred.createdAt
      }));

      console.log(`Loaded ${livePredictions.length} predictions from API:`, livePredictions);
      setPredictions(livePredictions);
      setLastUpdate(new Date());

    } catch (err) {
      console.error('Error loading predictions from API:', err);
      setError(err instanceof Error ? err.message : 'Failed to load predictions');

      toast({
        title: "API Connection Failed",
        description: err instanceof Error ? err.message : 'Could not connect to backend API',
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const toggleRealTime = () => {
    setIsRealTimeActive(!isRealTimeActive);
    toast({
      title: isRealTimeActive ? "Real-time Disabled" : "Real-time Enabled",
      description: isRealTimeActive ? "Stopped listening for updates" : "Now listening for live updates",
    });
  };

  const refreshPredictions = () => {
    loadPredictions();
    toast({
      title: "Refreshing",
      description: "Loading latest predictions from API",
    });
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 80) return 'text-success';
    if (confidence >= 60) return 'text-warning';
    return 'text-muted-foreground';
  };

  const getEdgeColor = (edge: number) => {
    if (edge >= 10) return 'text-success';
    if (edge >= 5) return 'text-warning';
    return 'text-muted-foreground';
  };

  if (loading) {
    return (
      <Card className="border-primary/20">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            Loading Live Predictions...
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[1, 2, 3].map((i) => (
              <div key={i} className="animate-pulse h-24 bg-muted rounded-md"></div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="border-destructive/20">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-destructive">
            <TrendingUp className="h-5 w-5" />
            API Connection Error
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 space-y-4">
            <p className="text-destructive">{error}</p>
            <Button onClick={refreshPredictions} variant="outline">
              <RefreshCw className="h-4 w-4 mr-2" />
              Retry Connection
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="border-primary/20">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5 text-primary" />
            Live Predictions from API
            <Badge variant="secondary">
              {predictions.length} Active
            </Badge>
          </CardTitle>
          
          {showControls && (
            <div className="flex items-center gap-2">
              {lastUpdate && (
                <span className="text-xs text-muted-foreground">
                  Last update: {format(lastUpdate, 'HH:mm:ss')}
                </span>
              )}
              <Button
                onClick={toggleRealTime}
                variant={isRealTimeActive ? "destructive" : "outline"}
                size="sm"
              >
                {isRealTimeActive ? <Pause className="w-4 h-4 mr-1" /> : <Play className="w-4 h-4 mr-1" />}
                {isRealTimeActive ? "Stop" : "Start"}
              </Button>
              <Button onClick={refreshPredictions} variant="outline" size="sm">
                <RefreshCw className="w-4 h-4" />
              </Button>
            </div>
          )}
        </div>
      </CardHeader>
      <CardContent>
        {predictions.length > 0 ? (
          <div className="space-y-4">
            {predictions.map((prediction) => (
              <Card key={prediction.id} className={`border ${prediction.is_premium ? 'border-warning/30' : 'border-border'}`}>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-2">
                      <Badge variant="outline" className="text-xs">
                        {prediction.sport}
                      </Badge>
                      <Badge variant="secondary" className="text-xs">
                        {prediction.league}
                      </Badge>
                      {prediction.is_premium && (
                        <Badge className="bg-gradient-primary text-xs">
                          Premium
                        </Badge>
                      )}
                    </div>
                    <div className="flex items-center gap-2 text-xs text-muted-foreground">
                      <Clock className="h-3 w-3" />
                      {format(new Date(prediction.match_date), 'MMM dd, HH:mm')}
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <h4 className="font-semibold text-sm mb-1">
                        {prediction.home_team} vs {prediction.away_team}
                      </h4>
                      <p className="text-xs text-muted-foreground">
                        {prediction.market}
                      </p>
                    </div>

                    <div className="space-y-2">
                      <div className="flex justify-between items-center">
                        <span className="text-sm font-medium">Pick:</span>
                        <Badge variant="default">{prediction.pick}</Badge>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm">Confidence:</span>
                        <span className={`text-sm font-bold ${getConfidenceColor(prediction.confidence)}`}>
                          {prediction.confidence}%
                        </span>
                      </div>
                      {prediction.edge !== undefined && (
                        <div className="flex justify-between items-center">
                          <span className="text-sm">Edge:</span>
                          <span className={`text-sm font-bold ${getEdgeColor(prediction.edge)}`}>
                            {prediction.edge > 0 ? '+' : ''}{prediction.edge.toFixed(1)}%
                          </span>
                        </div>
                      )}
                      <div className="flex justify-between items-center">
                        <span className="text-sm">Odds:</span>
                        <span className="text-sm font-medium">
                          {prediction.odds > 0 ? '+' : ''}{prediction.odds}
                        </span>
                      </div>
                    </div>
                  </div>

                  <div className="mt-2 text-xs text-muted-foreground">
                    Source: {prediction.source} | Created: {format(new Date(prediction.created_at), 'MMM dd, HH:mm')}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : (
          <div className="text-center py-8">
            <p className="text-muted-foreground mb-4">No live predictions available from API.</p>
            <Button onClick={refreshPredictions} variant="outline">
              <RefreshCw className="h-4 w-4 mr-2" />
              Check for Updates
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
};