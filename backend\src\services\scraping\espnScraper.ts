import { BaseScraper, ScrapingR<PERSON>ult, MatchData } from './baseScraper';
import { logger } from '@/utils/logger';

interface ESPNGame {
  id: string;
  date: string;
  status: {
    type: {
      name: string;
      state: string;
    };
  };
  competitions: Array<{
    id: string;
    competitors: Array<{
      id: string;
      team: {
        id: string;
        displayName: string;
        abbreviation: string;
      };
      homeAway: string;
      score: string;
    }>;
    odds?: Array<{
      provider: {
        name: string;
      };
      details: string;
      overUnder: number;
    }>;
  }>;
}

interface ESPNResponse {
  events: ESPNGame[];
  leagues: Array<{
    name: string;
    abbreviation: string;
  }>;
}

export class ESPNScraper extends BaseScraper {
  private sportMappings: { [key: string]: string } = {
    'football': 'football',
    'nfl': 'football',
    'basketball': 'basketball',
    'nba': 'basketball',
    'baseball': 'baseball',
    'mlb': 'baseball',
    'hockey': 'hockey',
    'nhl': 'hockey',
    'soccer': 'soccer',
    'mls': 'soccer',
  };

  private leagueMappings: { [key: string]: string } = {
    'nfl': 'nfl',
    'nba': 'nba',
    'mlb': 'mlb',
    'nhl': 'nhl',
    'mls': 'mls',
    'ncaaf': 'college-football',
    'ncaab': 'mens-college-basketball',
  };

  constructor() {
    super('ESPN', 'https://site.api.espn.com/apis/site/v2/sports');
  }

  async scrapeMatches(sport: string, league?: string): Promise<ScrapingResult> {
    try {
      const espnSport = this.mapSportToESPN(sport);
      const espnLeague = league ? this.mapLeagueToESPN(league) : this.getDefaultLeague(sport);

      if (!espnSport || !espnLeague) {
        return this.createResult(false, [], `Unsupported sport/league: ${sport}/${league}`);
      }

      const url = `${this.baseUrl}/${espnSport}/${espnLeague}/scoreboard`;
      logger.info(`ESPN: Scraping ${url}`);

      const data: ESPNResponse = await this.makeRequest(url);
      
      if (!data.events || !Array.isArray(data.events)) {
        return this.createResult(false, [], 'No events found in ESPN response');
      }

      const matches: MatchData[] = [];

      for (const game of data.events) {
        try {
          const match = this.parseESPNGame(game, sport, league || espnLeague);
          if (match && this.validateMatchData(match)) {
            matches.push(match);
          }
        } catch (error) {
          logger.warn(`ESPN: Failed to parse game ${game.id}: ${error}`);
        }
      }

      logger.info(`ESPN: Successfully scraped ${matches.length} matches for ${sport}/${league}`);
      return this.createResult(true, matches);

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      logger.error(`ESPN: Scraping failed: ${errorMessage}`);
      return this.createResult(false, [], errorMessage);
    }
  }

  private parseESPNGame(game: ESPNGame, sport: string, league: string): MatchData | null {
    if (!game.competitions || game.competitions.length === 0) {
      return null;
    }

    const competition = game.competitions[0];
    const competitors = competition.competitors;

    if (!competitors || competitors.length !== 2) {
      return null;
    }

    const homeTeam = competitors.find(c => c.homeAway === 'home');
    const awayTeam = competitors.find(c => c.homeAway === 'away');

    if (!homeTeam || !awayTeam) {
      return null;
    }

    const matchDate = new Date(game.date);
    const homeTeamName = this.normalizeTeamName(homeTeam.team.displayName);
    const awayTeamName = this.normalizeTeamName(awayTeam.team.displayName);

    const match: MatchData = {
      matchId: this.generateMatchId(homeTeamName, awayTeamName, matchDate, league),
      sport,
      league: league.toUpperCase(),
      homeTeam: homeTeamName,
      awayTeam: awayTeamName,
      matchDate,
      source: 'ESPN',
      status: this.mapESPNStatus(game.status.type.state),
    };

    // Add scores if available
    if (homeTeam.score && awayTeam.score) {
      match.homeScore = parseInt(homeTeam.score) || 0;
      match.awayScore = parseInt(awayTeam.score) || 0;
    }

    // Add odds if available
    if (competition.odds && competition.odds.length > 0) {
      const odds = competition.odds[0];
      match.odds = this.parseESPNOdds(odds);
    }

    // Add basic stats (ESPN doesn't provide detailed stats in scoreboard API)
    match.stats = {
      home_rating: this.generateRating(homeTeamName),
      away_rating: this.generateRating(awayTeamName),
    };

    return match;
  }

  private parseESPNOdds(odds: any): any {
    const result: any = {};

    if (odds.details) {
      // Parse moneyline from details string
      const moneylineMatch = odds.details.match(/([+-]\d+)/g);
      if (moneylineMatch && moneylineMatch.length >= 2) {
        result.moneyline = {
          away: parseInt(moneylineMatch[0]),
          home: parseInt(moneylineMatch[1]),
        };
      }
    }

    if (odds.overUnder) {
      result.total = {
        line: odds.overUnder,
        over: -110,
        under: -110,
      };
    }

    return result;
  }

  private mapESPNStatus(status: string): string {
    switch (status.toLowerCase()) {
      case 'pre':
        return 'UPCOMING';
      case 'in':
        return 'LIVE';
      case 'post':
        return 'COMPLETED';
      default:
        return 'UPCOMING';
    }
  }

  private mapSportToESPN(sport: string): string | null {
    return this.sportMappings[sport.toLowerCase()] || null;
  }

  private mapLeagueToESPN(league: string): string | null {
    return this.leagueMappings[league.toLowerCase()] || null;
  }

  private getDefaultLeague(sport: string): string {
    const defaults: { [key: string]: string } = {
      'football': 'nfl',
      'basketball': 'nba',
      'baseball': 'mlb',
      'hockey': 'nhl',
      'soccer': 'mls',
    };
    return defaults[sport.toLowerCase()] || 'nfl';
  }

  private generateRating(teamName: string): number {
    // Simple hash-based rating generation for demo purposes
    // In production, this would come from actual team statistics
    let hash = 0;
    for (let i = 0; i < teamName.length; i++) {
      const char = teamName.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash % 30) + 70; // Rating between 70-100
  }
}
