import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";
import path from "path";
import { componentTagger } from "lovable-tagger";

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => ({
  server: {
    // Bind on all interfaces (IPv4) and keep a stable dev port & HMR client
    host: true,            // 0.0.0.0
    port: 8080,
    strictPort: true,      // don't auto-bump to 8081 (keeps HMR consistent)
    hmr: {
      protocol: 'ws',
      host: '127.0.0.1',   // stabilize HMR client when network changes (Windows/Wi‑Fi/VPN)
      port: 8080,
    },
  },
  plugins: [
    react(),
    mode === 'development' &&
    componentTagger(),
  ].filter(Boolean),
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  // Explicitly exclude backend files
  optimizeDeps: {
    exclude: ['backend']
  },
  build: {
    rollupOptions: {
      external: (id) => id.includes('backend/') || id.includes('\\backend\\')
    }
  },
  // Only include src directory
  root: '.',
  publicDir: 'public'
}));
