import React, { useEffect, useRef } from 'react';
import { cn } from '@/lib/utils';

declare global {
  interface Window { adsbygoogle: any[] }
}

interface AdUnitProps {
  slot?: string;
  format?: 'auto' | 'rectangle' | 'vertical' | 'horizontal';
  className?: string;
  style?: React.CSSProperties;
}

const GOOGLE_CLIENT = 'ca-pub-8858971958937267';

function AdUnit({ slot, format = 'auto', className, style }: AdUnitProps) {
  const ref = useRef<HTMLDivElement>(null);

  useEffect(() => {
    try {
      // Ensure the global queue exists
      if (typeof window !== 'undefined') {
        // @ts-ignore
        (window.adsbygoogle = window.adsbygoogle || []).push({});
      }
    } catch (e) {
      // Swallow to avoid crashing the app in dev or if blocked by extensions
      // console.warn('adsbygoogle push failed', e);
    }
  }, []);

  return (
    <div className={className} ref={ref}>
      <ins
        className="adsbygoogle"
        style={{ display: 'block', ...(style || {}) }}
        data-ad-client={GOOGLE_CLIENT}
        {...(slot ? { 'data-ad-slot': slot } : {})}
        data-ad-format={format}
        data-full-width-responsive="true"
      />
    </div>
  );
}

export const AdSenseHeader: React.FC<{ className?: string; slot?: string }> = ({ className, slot }) => (
  <AdUnit
    className={cn('my-4', className)}
    slot={slot}
    // Typical header sizes; format auto lets Google choose
    format="auto"
    style={{ textAlign: 'center' }}
  />
);

export const AdSenseSidebar: React.FC<{ className?: string; slot?: string }> = ({ className, slot }) => (
  <AdUnit className={cn('my-4', className)} slot={slot} format="auto" />
);

export const AdSenseInline: React.FC<{ className?: string; slot?: string }> = ({ className, slot }) => (
  <AdUnit className={cn('my-4', className)} slot={slot} format="auto" />
);

export const AdSenseFooter: React.FC<{ className?: string; slot?: string }> = ({ className, slot }) => (
  <AdUnit className={cn('my-4', className)} slot={slot} format="auto" />
);
