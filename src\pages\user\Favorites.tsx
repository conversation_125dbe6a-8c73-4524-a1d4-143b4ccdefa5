import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Heart, Star, TrendingUp, Calendar } from 'lucide-react';

const UserFavorites: React.FC = () => {
  const favorites = [
    {
      id: 1,
      match: "Manchester United vs Liverpool",
      league: "Premier League",
      prediction: "Over 2.5 Goals",
      confidence: 88,
      odds: 1.85,
      date: "2024-01-20"
    },
    {
      id: 2,
      match: "Juventus vs Inter Milan",
      league: "Serie A",
      prediction: "Juventus Win",
      confidence: 75,
      odds: 2.20,
      date: "2024-01-18"
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-hero">
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bruno tracking-wide mb-2">My Favorites</h1>
          <p className="text-muted-foreground">Your saved predictions and preferred matches</p>
        </div>

        {favorites.length === 0 ? (
          <Card className="bg-gradient-card text-center py-12">
            <CardContent>
              <Heart className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
              <h3 className="text-lg font-semibold mb-2">No favorites yet</h3>
              <p className="text-muted-foreground mb-4">Start adding predictions to your favorites to see them here</p>
              <Button asChild>
                <a href="/predictions">Browse Predictions</a>
              </Button>
            </CardContent>
          </Card>
        ) : (
          <div className="space-y-4">
            {favorites.map((favorite) => (
              <Card key={favorite.id} className="bg-gradient-card">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div>
                      <h3 className="font-semibold text-lg">{favorite.match}</h3>
                      <p className="text-muted-foreground text-sm">{favorite.league}</p>
                    </div>
                    <Button variant="ghost" size="sm">
                      <Heart className="h-4 w-4 fill-current text-destructive" />
                    </Button>
                  </div>
                  
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                    <div>
                      <p className="text-sm text-muted-foreground">Prediction</p>
                      <p className="font-medium">{favorite.prediction}</p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Confidence</p>
                      <div className="flex items-center gap-1">
                        <Star className="h-4 w-4 text-warning" />
                        <span className="font-medium">{favorite.confidence}%</span>
                      </div>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Odds</p>
                      <p className="font-medium">{favorite.odds}</p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Match Date</p>
                      <div className="flex items-center gap-1">
                        <Calendar className="h-4 w-4" />
                        <span className="font-medium text-sm">{favorite.date}</span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex gap-2">
                    <Button size="sm">Place Bet</Button>
                    <Button variant="outline" size="sm">View Analysis</Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default UserFavorites;