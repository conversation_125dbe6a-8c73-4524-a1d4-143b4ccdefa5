import React, { useState, useEffect } from 'react';
import { Header } from '@/components/Header';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { api, SportsData } from '@/services/api';
import { useToast } from '@/hooks/use-toast';
import { Database, Zap, Globe, RefreshCw, Play, Pause, Search, Filter, Download, Upload } from 'lucide-react';

interface ScrapingStats {
  totalMatches: number;
  recentMatches: number;
  upcomingMatches: number;
  completedMatches: number;
}

const SportsDataPipeline: React.FC = () => {
  const { toast } = useToast();
  const [sportsData, setSportsData] = useState<SportsData[]>([]);
  const [stats, setStats] = useState<ScrapingStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [scraping, setScraping] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [sportFilter, setSportFilter] = useState('all');
  const [statusFilter, setStatusFilter] = useState('all');
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  const fetchSportsData = async () => {
    try {
      setLoading(true);
      const response = await api.getSportsData({
        page,
        limit: 20,
        sport: sportFilter !== 'all' ? sportFilter : undefined,
        status: statusFilter !== 'all' ? statusFilter : undefined
      });

      if (response.success && response.data) {
        setSportsData(response.data.matches);
        setTotalPages(response.data.pagination?.pages || 1);
      }
    } catch (error) {
      console.error('Error fetching sports data:', error);
      toast({
        title: "Error",
        description: "Failed to load sports data",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const fetchStats = async () => {
    try {
      const response = await api.getSportsDataStats();
      if (response.success && response.data) {
        setStats(response.data);
      }
    } catch (error) {
      console.error('Error fetching stats:', error);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await Promise.all([fetchSportsData(), fetchStats()]);
  };

  const handleStartScraping = async (provider: string, sport: string, league?: string) => {
    try {
      setScraping(true);
      const response = await api.startScraping(provider, sport, league);

      if (response.success) {
        toast({
          title: "Scraping Started",
          description: `Started scraping ${sport} data from ${provider}`,
        });
        // Refresh data after a delay
        setTimeout(() => {
          fetchSportsData();
          fetchStats();
        }, 3000);
      } else {
        throw new Error(response.error);
      }
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to start scraping",
        variant: "destructive",
      });
    } finally {
      setScraping(false);
    }
  };

  const handleComprehensiveScraping = async (sport: string, league?: string) => {
    try {
      setScraping(true);
      const response = await api.startComprehensiveScraping(sport, league);

      if (response.success) {
        toast({
          title: "Comprehensive Scraping Started",
          description: `Started comprehensive scraping for ${sport}`,
        });
        // Refresh data after a delay
        setTimeout(() => {
          fetchSportsData();
          fetchStats();
        }, 5000);
      } else {
        throw new Error(response.error);
      }
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to start comprehensive scraping",
        variant: "destructive",
      });
    } finally {
      setScraping(false);
    }
  };

  useEffect(() => {
    fetchSportsData();
    fetchStats();
  }, [page, sportFilter, statusFilter]);

  const filteredData = sportsData.filter(match => {
    const matchesSearch = searchQuery === '' ||
      match.homeTeam.toLowerCase().includes(searchQuery.toLowerCase()) ||
      match.awayTeam.toLowerCase().includes(searchQuery.toLowerCase()) ||
      match.league.toLowerCase().includes(searchQuery.toLowerCase());

    return matchesSearch;
  });

  return (
    <div className="min-h-screen bg-background">
      <Header />

      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold mb-2">Sports Data Pipeline</h1>
            <p className="text-muted-foreground">Manage sports data scraping and processing</p>
          </div>
          <div className="flex gap-4">
            <Button
              onClick={handleRefresh}
              disabled={refreshing}
              variant="outline"
              size="sm"
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          {loading || !stats ? (
            [...Array(4)].map((_, i) => (
              <Card key={i}>
                <CardContent className="p-6">
                  <div className="flex items-center space-x-4">
                    <div className="h-12 w-12 bg-muted animate-pulse rounded-lg"></div>
                    <div>
                      <div className="h-6 bg-muted animate-pulse rounded w-16 mb-2"></div>
                      <div className="h-4 bg-muted animate-pulse rounded w-20"></div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))
          ) : (
            <>
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center space-x-4">
                    <div className="h-12 w-12 bg-primary/10 rounded-lg flex items-center justify-center">
                      <Database className="h-6 w-6 text-primary" />
                    </div>
                    <div>
                      <div className="text-2xl font-bold">{stats.totalMatches.toLocaleString()}</div>
                      <div className="text-sm text-muted-foreground">Total Matches</div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center space-x-4">
                    <div className="h-12 w-12 bg-success/10 rounded-lg flex items-center justify-center">
                      <Globe className="h-6 w-6 text-success" />
                    </div>
                    <div>
                      <div className="text-2xl font-bold">{stats.recentMatches.toLocaleString()}</div>
                      <div className="text-sm text-muted-foreground">Recent Matches</div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center space-x-4">
                    <div className="h-12 w-12 bg-warning/10 rounded-lg flex items-center justify-center">
                      <Zap className="h-6 w-6 text-warning" />
                    </div>
                    <div>
                      <div className="text-2xl font-bold">{stats.upcomingMatches.toLocaleString()}</div>
                      <div className="text-sm text-muted-foreground">Upcoming Matches</div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center space-x-4">
                    <div className="h-12 w-12 bg-info/10 rounded-lg flex items-center justify-center">
                      <Database className="h-6 w-6 text-info" />
                    </div>
                    <div>
                      <div className="text-2xl font-bold">{stats.completedMatches.toLocaleString()}</div>
                      <div className="text-sm text-muted-foreground">Completed Matches</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </>
          )}
        </div>

        <Tabs defaultValue="data" className="space-y-6">
          <TabsList>
            <TabsTrigger value="data">Sports Data</TabsTrigger>
            <TabsTrigger value="scraping">Scraping Controls</TabsTrigger>
            <TabsTrigger value="pipeline">Pipeline Status</TabsTrigger>
          </TabsList>

          <TabsContent value="data">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Database className="h-5 w-5" />
                  Sports Data Management
                </CardTitle>
                <CardDescription>View and manage scraped sports data</CardDescription>
              </CardHeader>
              <CardContent>
                {/* Filters */}
                <div className="flex flex-col sm:flex-row gap-4 mb-6">
                  <div className="flex items-center space-x-2 bg-muted/50 rounded-lg px-3 py-2 flex-1">
                    <Search className="w-4 h-4 text-muted-foreground" />
                    <Input
                      placeholder="Search matches..."
                      className="border-0 bg-transparent text-sm focus-visible:ring-0 p-0"
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                    />
                  </div>

                  <Select value={sportFilter} onValueChange={setSportFilter}>
                    <SelectTrigger className="w-[150px]">
                      <SelectValue placeholder="Filter by sport" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Sports</SelectItem>
                      <SelectItem value="football">Football</SelectItem>
                      <SelectItem value="basketball">Basketball</SelectItem>
                      <SelectItem value="tennis">Tennis</SelectItem>
                      <SelectItem value="baseball">Baseball</SelectItem>
                    </SelectContent>
                  </Select>

                  <Select value={statusFilter} onValueChange={setStatusFilter}>
                    <SelectTrigger className="w-[150px]">
                      <SelectValue placeholder="Filter by status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Status</SelectItem>
                      <SelectItem value="UPCOMING">Upcoming</SelectItem>
                      <SelectItem value="LIVE">Live</SelectItem>
                      <SelectItem value="COMPLETED">Completed</SelectItem>
                      <SelectItem value="POSTPONED">Postponed</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Sports Data Table */}
                <div className="border rounded-lg">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Match</TableHead>
                        <TableHead>League</TableHead>
                        <TableHead>Date</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Score</TableHead>
                        <TableHead>Source</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {loading ? (
                        [...Array(5)].map((_, i) => (
                          <TableRow key={i}>
                            <TableCell>
                              <div className="space-y-2">
                                <div className="h-4 bg-muted animate-pulse rounded w-32"></div>
                                <div className="h-3 bg-muted animate-pulse rounded w-24"></div>
                              </div>
                            </TableCell>
                            <TableCell><div className="h-4 bg-muted animate-pulse rounded w-20"></div></TableCell>
                            <TableCell><div className="h-4 bg-muted animate-pulse rounded w-24"></div></TableCell>
                            <TableCell><div className="h-6 bg-muted animate-pulse rounded w-16"></div></TableCell>
                            <TableCell><div className="h-4 bg-muted animate-pulse rounded w-12"></div></TableCell>
                            <TableCell><div className="h-4 bg-muted animate-pulse rounded w-16"></div></TableCell>
                          </TableRow>
                        ))
                      ) : filteredData.length === 0 ? (
                        <TableRow>
                          <TableCell colSpan={6} className="text-center py-8 text-muted-foreground">
                            No sports data found
                          </TableCell>
                        </TableRow>
                      ) : (
                        filteredData.map((match) => (
                          <TableRow key={match.id}>
                            <TableCell>
                              <div>
                                <div className="font-medium">
                                  {match.homeTeam} vs {match.awayTeam}
                                </div>
                                <div className="text-sm text-muted-foreground">
                                  {match.sport.charAt(0).toUpperCase() + match.sport.slice(1)}
                                </div>
                              </div>
                            </TableCell>
                            <TableCell className="text-sm">{match.league}</TableCell>
                            <TableCell className="text-sm">
                              {new Date(match.matchDate).toLocaleDateString()} {new Date(match.matchDate).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                            </TableCell>
                            <TableCell>
                              <Badge
                                variant={
                                  match.status === 'LIVE' ? 'default' :
                                  match.status === 'COMPLETED' ? 'secondary' :
                                  match.status === 'UPCOMING' ? 'outline' :
                                  'destructive'
                                }
                              >
                                {match.status}
                              </Badge>
                            </TableCell>
                            <TableCell className="font-mono text-sm">
                              {match.homeScore !== null && match.awayScore !== null
                                ? `${match.homeScore} - ${match.awayScore}`
                                : '-'
                              }
                            </TableCell>
                            <TableCell className="text-sm text-muted-foreground">
                              {match.source || 'Manual'}
                            </TableCell>
                          </TableRow>
                        ))
                      )}
                    </TableBody>
                  </Table>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="scraping">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Globe className="h-5 w-5" />
                    Quick Scraping
                  </CardTitle>
                  <CardDescription>Start scraping from individual providers</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <Button
                      onClick={() => handleStartScraping('espn', 'football')}
                      disabled={scraping}
                      variant="outline"
                    >
                      <Globe className="h-4 w-4 mr-2" />
                      ESPN Football
                    </Button>
                    <Button
                      onClick={() => handleStartScraping('espn', 'basketball')}
                      disabled={scraping}
                      variant="outline"
                    >
                      <Globe className="h-4 w-4 mr-2" />
                      ESPN Basketball
                    </Button>
                    <Button
                      onClick={() => handleStartScraping('odds-api', 'football')}
                      disabled={scraping}
                      variant="outline"
                    >
                      <Zap className="h-4 w-4 mr-2" />
                      Odds API Football
                    </Button>
                    <Button
                      onClick={() => handleStartScraping('odds-api', 'basketball')}
                      disabled={scraping}
                      variant="outline"
                    >
                      <Zap className="h-4 w-4 mr-2" />
                      Odds API Basketball
                    </Button>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Database className="h-5 w-5" />
                    Comprehensive Scraping
                  </CardTitle>
                  <CardDescription>Run full scraping across all providers</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-3">
                    <Button
                      onClick={() => handleComprehensiveScraping('football')}
                      disabled={scraping}
                      className="w-full"
                    >
                      {scraping ? (
                        <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                      ) : (
                        <Play className="h-4 w-4 mr-2" />
                      )}
                      Scrape All Football Data
                    </Button>
                    <Button
                      onClick={() => handleComprehensiveScraping('basketball')}
                      disabled={scraping}
                      className="w-full"
                      variant="outline"
                    >
                      {scraping ? (
                        <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                      ) : (
                        <Play className="h-4 w-4 mr-2" />
                      )}
                      Scrape All Basketball Data
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="pipeline">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Zap className="h-5 w-5" />
                  Pipeline Status
                </CardTitle>
                <CardDescription>Monitor the data processing pipeline</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className="text-center space-y-2">
                    <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center mx-auto">
                      <Globe className="w-6 h-6 text-primary" />
                    </div>
                    <h3 className="font-semibold">1. Data Scraping</h3>
                    <p className="text-sm text-muted-foreground">
                      Scrape live sports data from multiple sources
                    </p>
                    <Badge variant="outline" className="text-xs">
                      {scraping ? 'Running' : 'Idle'}
                    </Badge>
                  </div>
                  <div className="text-center space-y-2">
                    <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center mx-auto">
                      <Database className="w-6 h-6 text-primary" />
                    </div>
                    <h3 className="font-semibold">2. Data Processing</h3>
                    <p className="text-sm text-muted-foreground">
                      Process and normalize data into structured format
                    </p>
                    <Badge variant="outline" className="text-xs">
                      Active
                    </Badge>
                  </div>
                  <div className="text-center space-y-2">
                    <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center mx-auto">
                      <Zap className="w-6 h-6 text-primary" />
                    </div>
                    <h3 className="font-semibold">3. AI Predictions</h3>
                    <p className="text-sm text-muted-foreground">
                      Generate AI-powered predictions based on processed data
                    </p>
                    <Badge variant="outline" className="text-xs">
                      Ready
                    </Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default SportsDataPipeline;