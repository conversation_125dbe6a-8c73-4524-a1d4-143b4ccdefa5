import React from 'react';
import { Header } from '@/components/Header';
import MinimalFooter from '@/components/MinimalFooter';
import { WebScraperInterface } from '@/components/WebScraperInterface';
import { SEOHead } from '@/components/SEOHead';

const WebScraper: React.FC = () => {
  const breadcrumbs = [
    { name: 'Home', url: 'https://1300blk.online' },
    { name: 'Web Scraper', url: 'https://1300blk.online/web-scraper' }
  ];

  return (
    <>
      <SEOHead
        title="Advanced Web Scraper - ML Sports Intelligence Engine"
        description="Powerful web scraping service with CORS bypass, caching, and intelligent data extraction. Perfect for sports data, betting odds, and market analysis."
        keywords="web scraper, data extraction, sports data scraping, CORS bypass, automated scraping"
        canonical="https://1300blk.online/web-scraper"
        breadcrumbs={breadcrumbs}
      />

      <div className="min-h-screen bg-background">
        <Header />
        
        <main className="pt-20">
          <div className="container mx-auto px-4 py-8">
            <div className="text-center mb-8">
              <h1 className="text-4xl font-orbitron font-bold mb-4">
                Advanced Web Scraper
              </h1>
              <p className="text-xl text-muted-foreground">
                Extract data from any website with our intelligent scraping service
              </p>
            </div>
            
            <WebScraperInterface />
          </div>
        </main>
        
        <MinimalFooter />
      </div>
    </>
  );
};

export default WebScraper;