from fastapi import FastAP<PERSON>, HTTPException
from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from app.config import settings
from app.scrapers.base import ScrapeRequest
from app.scrapers.bs_scraper import scrape_static
from app.scrapers.playwright_scraper import scrape_dynamic
from app.scrapers.providers import fetch_espn_scoreboard
from app.ml.model import predict_proba, expected_value
from app.services.supabase_client import supabase_service

app = FastAPI(title="Scraping + ML Service", version="0.2.0")


class ScrapeBody(BaseModel):
    url: str
    use_js: bool = False
    proxy_url: Optional[str] = None
    wait_ms: int = 1000
    headers: Optional[Dict[str, str]] = None
    timeout: Optional[int] = None


class ScrapeProviderBody(BaseModel):
    provider: str
    sport: str
    league: Optional[str] = None


class PredictBody(BaseModel):
    matches: List[Dict[str, Any]] = Field(default_factory=list)
    market: str = "Moneyline"


class IngestBody(BaseModel):
    table: str
    rows: List[Dict[str, Any]]


@app.get("/health")
async def health() -> Dict[str, Any]:
    return {"ok": True}


@app.post("/scrape")
async def scrape(body: ScrapeBody) -> Dict[str, Any]:
    req = ScrapeRequest(
        url=body.url,
        use_js=body.use_js,
        proxy_url=body.proxy_url or settings.PROXY_URL,
        wait_ms=body.wait_ms,
        headers=body.headers,
        timeout=body.timeout or settings.REQUEST_TIMEOUT_SECONDS,
    )
    if req.use_js:
        res = scrape_dynamic(req)
    else:
        res = scrape_static(req)
    if not res.get("success"):
        raise HTTPException(status_code=500, detail=res.get("error", "scrape failed"))
    return res


@app.post("/scrape_provider")
async def scrape_provider(body: ScrapeProviderBody) -> Dict[str, Any]:
    prov = body.provider.lower()
    try:
        if prov == "espn":
            data = fetch_espn_scoreboard(body.sport, body.league)
        else:
            raise HTTPException(status_code=400, detail=f"Unsupported provider: {body.provider}")
        return {"success": True, "data": data}
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/predict")
async def predict(body: PredictBody) -> Dict[str, Any]:
    # Map incoming matches to feature vectors
    feats: List[Dict[str, Any]] = []
    for m in body.matches:
        feats.append({
            "home_rating": float(m.get("stats", {}).get("home_rating", 75)),
            "away_rating": float(m.get("stats", {}).get("away_rating", 75)),
            "edge": float(m.get("edge", 0.0)),
        })
    probs = predict_proba(feats)

    picks = []
    for i, m in enumerate(body.matches):
        home_prob = probs[i]
        away_prob = 1 - home_prob
        home_odds = float(m.get("odds", {}).get("moneyline", {}).get("home", -110))
        away_odds = float(m.get("odds", {}).get("moneyline", {}).get("away", 110))
        ev_home = expected_value(home_prob, home_odds)
        ev_away = expected_value(away_prob, away_odds)
        if ev_home > ev_away:
            pick = f"{m.get('home_team','HOME')} ML"
            prob = home_prob
            odds = home_odds
            ev = ev_home
        else:
            pick = f"{m.get('away_team','AWAY')} ML"
            prob = away_prob
            odds = away_odds
            ev = ev_away
        picks.append({
            "match_id": m.get("match_id"),
            "market": body.market,
            "pick": pick,
            "confidence": round(prob * 100, 1),
            "odds": odds,
            "expected_value": round(ev, 4),
        })

    return {"success": True, "data": picks}


@app.post("/ingest")
async def ingest(body: IngestBody) -> Dict[str, Any]:
    try:
        count = supabase_service.upsert(body.table, body.rows)
        return {"success": True, "rows": count}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/pipeline/scrape_predict")
async def pipeline_scrape_predict(body: ScrapeProviderBody) -> Dict[str, Any]:
    """Fetch matches from provider, upsert into sports_data, run predictions, return results."""
    # 1) Scrape provider
    prov_data = await scrape_provider(body)
    matches = prov_data.get("data", [])

    # 2) Ingest into sports_data
    try:
        supabase_service.upsert("sports_data", matches)
    except Exception as e:
        # Non-fatal; continue to prediction
        pass

    # 3) Predict
    pred_body = PredictBody(matches=matches, market="Moneyline")
    pred = await predict(pred_body)  # reuse endpoint logic

    # 4) Optionally write predictions to live_predictions
    try:
        to_insert = []
        for m, p in zip(matches, pred.get("data", [])):
            to_insert.append({
                "sport": m.get("sport"),
                "league": m.get("league"),
                "home_team": m.get("home_team"),
                "away_team": m.get("away_team"),
                "match_date": m.get("match_date"),
                "market": p.get("market"),
                "pick": p.get("pick"),
                "confidence": p.get("confidence"),
                "odds": p.get("odds"),
                "edge": p.get("expected_value"),
                "expected_value": p.get("expected_value"),
                "factors": {},
                "source": "PY_SERVICE",
                "status": "upcoming",
                "is_premium": (p.get("confidence", 0) or 0) >= 75,
            })
        if to_insert:
            supabase_service.insert("live_predictions", to_insert)
    except Exception:
        pass

    return {"success": True, "matches": matches, "predictions": pred.get("data", [])}
