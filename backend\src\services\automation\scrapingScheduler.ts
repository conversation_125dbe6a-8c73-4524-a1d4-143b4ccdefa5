import cron from 'node-cron';
import { ScrapingManager } from '../scraping/scrapingManager';
import { PredictionEngine } from '../ml/predictionEngine';
import { prisma } from '@/config/database';
import { logger } from '@/utils/logger';

interface ScheduleConfig {
  enabled: boolean;
  sports: string[];
  leagues: string[];
  interval: string; // cron expression
  providers: string[];
  autoPredictions: boolean;
  maxConcurrent: number;
}

export class ScrapingScheduler {
  private scrapingManager: ScrapingManager;
  private predictionEngine: PredictionEngine;
  private scheduledTasks: Map<string, cron.ScheduledTask> = new Map();
  private isRunning: boolean = false;

  constructor() {
    this.scrapingManager = new ScrapingManager();
    this.predictionEngine = new PredictionEngine();
  }

  async initialize(): Promise<void> {
    logger.info('Initializing Scraping Scheduler...');
    
    // Load configuration from database
    const config = await this.loadConfiguration();
    
    if (config.enabled) {
      await this.startScheduledScraping(config);
    }

    logger.info('Scraping Scheduler initialized');
  }

  private async loadConfiguration(): Promise<ScheduleConfig> {
    try {
      const settings = await prisma.globalSettings.findMany({
        where: {
          key: { contains: 'scraping' }
        }
      });

      const config: ScheduleConfig = {
        enabled: this.getSettingValue(settings, 'scraping_enabled', true),
        sports: this.getSettingValue(settings, 'scraping_sports', ['football', 'basketball', 'baseball']),
        leagues: this.getSettingValue(settings, 'scraping_leagues', ['nfl', 'nba', 'mlb']),
        interval: this.getSettingValue(settings, 'scraping_interval', '0 */6 * * *'), // Every 6 hours
        providers: this.getSettingValue(settings, 'scraping_providers', ['espn', 'odds-api']),
        autoPredictions: this.getSettingValue(settings, 'auto_predictions', true),
        maxConcurrent: this.getSettingValue(settings, 'max_concurrent_scrapes', 3),
      };

      return config;
    } catch (error) {
      logger.error('Failed to load scraping configuration:', error);
      // Return default configuration
      return {
        enabled: true,
        sports: ['football', 'basketball', 'baseball'],
        leagues: ['nfl', 'nba', 'mlb'],
        interval: '0 */6 * * *',
        providers: ['espn', 'odds-api'],
        autoPredictions: true,
        maxConcurrent: 3,
      };
    }
  }

  private getSettingValue(settings: any[], key: string, defaultValue: any): any {
    const setting = settings.find(s => s.key === key);
    if (!setting) return defaultValue;
    
    try {
      return setting.type === 'json' ? JSON.parse(setting.value) : setting.value;
    } catch {
      return defaultValue;
    }
  }

  async startScheduledScraping(config: ScheduleConfig): Promise<void> {
    if (this.isRunning) {
      logger.warn('Scheduled scraping is already running');
      return;
    }

    logger.info(`Starting scheduled scraping with interval: ${config.interval}`);

    // Create main scraping task
    const task = cron.schedule(config.interval, async () => {
      await this.executeScrapingCycle(config);
    }, {
      scheduled: false,
      timezone: 'America/New_York'
    });

    this.scheduledTasks.set('main', task);
    task.start();
    this.isRunning = true;

    // Also schedule immediate execution for testing
    setTimeout(() => {
      this.executeScrapingCycle(config);
    }, 5000); // 5 seconds delay

    logger.info('Scheduled scraping started successfully');
  }

  async executeScrapingCycle(config: ScheduleConfig): Promise<void> {
    const startTime = Date.now();
    logger.info('Starting automated scraping cycle...');

    try {
      // Create scraping job record
      const job = await prisma.scrapingJob.create({
        data: {
          provider: 'automated',
          sport: config.sports.join(','),
          league: config.leagues.join(','),
          status: 'RUNNING',
          startedAt: new Date(),
        }
      });

      let totalMatches = 0;
      let totalPredictions = 0;
      const errors: string[] = [];

      // Process each sport/league combination
      for (const sport of config.sports) {
        for (const league of config.leagues) {
          try {
            logger.info(`Scraping ${sport}/${league}...`);
            
            // Scrape from all providers
            const result = await this.scrapingManager.scrapeAllProviders(sport, league);
            
            if (result.success) {
              totalMatches += result.totalMatches;
              logger.info(`Scraped ${result.totalMatches} matches for ${sport}/${league}`);

              // Generate predictions if enabled
              if (config.autoPredictions && result.totalMatches > 0) {
                const predictionCount = await this.generatePredictionsForSport(sport, league);
                totalPredictions += predictionCount;
              }
            } else {
              errors.push(`Failed to scrape ${sport}/${league}: ${result.errors?.join(', ') || 'Unknown error'}`);
            }

            // Small delay between requests to avoid rate limiting
            await this.delay(2000);

          } catch (error) {
            const errorMsg = `Error scraping ${sport}/${league}: ${error}`;
            logger.error(errorMsg);
            errors.push(errorMsg);
          }
        }
      }

      // Update job status
      await prisma.scrapingJob.update({
        where: { id: job.id },
        data: {
          status: errors.length > 0 ? 'COMPLETED_WITH_ERRORS' : 'COMPLETED',
          completedAt: new Date(),
          error: errors.length > 0 ? errors.join('; ') : null,
          results: JSON.stringify({
            totalMatches,
            totalPredictions,
            errors,
            duration: Date.now() - startTime,
          }),
        }
      });

      logger.info(`Scraping cycle completed: ${totalMatches} matches, ${totalPredictions} predictions in ${Date.now() - startTime}ms`);

    } catch (error) {
      logger.error('Scraping cycle failed:', error);
      
      // Log the error to database if possible
      try {
        await prisma.auditLog.create({
          data: {
            action: 'SCRAPING_CYCLE_ERROR',
            resource: 'automation',
            details: JSON.stringify({ error: (error as Error).toString() }),
          }
        });
      } catch (dbError) {
        logger.error('Failed to log scraping error to database:', dbError);
      }
    }
  }

  private async generatePredictionsForSport(sport: string, league: string): Promise<number> {
    try {
      // Get recent matches that don't have predictions yet
      const matches = await prisma.sportsData.findMany({
        where: {
          sport,
          league,
          matchDate: {
            gte: new Date(Date.now() - 24 * 60 * 60 * 1000), // Last 24 hours
          },
          status: {
            in: ['upcoming', 'scheduled']
          }
        },
        take: 50, // Limit to avoid overwhelming the system
      });

      let predictionCount = 0;

      for (const match of matches) {
        try {
          // Check if predictions already exist
          const existingPredictions = await prisma.prediction.findFirst({
            where: {
              sport: match.sport,
              league: match.league,
              homeTeam: match.homeTeam,
              awayTeam: match.awayTeam,
              matchDate: match.matchDate,
            }
          });

          if (!existingPredictions) {
            // Generate predictions
            // Convert database match to MatchData format
            const matchData = {
              matchId: match.externalId || match.id,
              externalId: match.externalId || undefined,
              sport: match.sport,
              league: match.league,
              homeTeam: match.homeTeam,
              awayTeam: match.awayTeam,
              matchDate: match.matchDate,
              odds: match.odds ? JSON.parse(match.odds) : undefined,
              stats: match.stats ? JSON.parse(match.stats) : undefined,
              source: match.source,
              status: match.status,
              homeScore: match.homeScore || undefined,
              awayScore: match.awayScore || undefined,
            };

            const predictions = await this.predictionEngine.generatePredictions(matchData);
            
            // Save predictions to database
            for (const prediction of predictions) {
              await prisma.prediction.create({
                data: {
                  sport: match.sport,
                  league: match.league,
                  homeTeam: match.homeTeam,
                  awayTeam: match.awayTeam,
                  matchDate: match.matchDate,
                  market: prediction.market,
                  pick: prediction.pick,
                  confidence: prediction.confidence,
                  odds: prediction.odds,
                  // edge: prediction.expectedValue, // Field doesn't exist in schema
                  expectedValue: prediction.expectedValue,
                  factors: JSON.stringify(prediction.factors),
                  source: 'AUTO_PREDICTION',
                  status: 'upcoming',
                  isPremium: prediction.confidence >= 75,
                }
              });
              predictionCount++;
            }
          }
        } catch (error) {
          logger.warn(`Failed to generate prediction for match ${match.id}:`, error);
        }
      }

      logger.info(`Generated ${predictionCount} predictions for ${sport}/${league}`);
      return predictionCount;

    } catch (error) {
      logger.error(`Failed to generate predictions for ${sport}/${league}:`, error);
      return 0;
    }
  }

  async stopScheduledScraping(): Promise<void> {
    logger.info('Stopping scheduled scraping...');
    
    for (const [name, task] of this.scheduledTasks) {
      task.stop();
      logger.info(`Stopped scheduled task: ${name}`);
    }
    
    this.scheduledTasks.clear();
    this.isRunning = false;
    
    logger.info('Scheduled scraping stopped');
  }

  async updateConfiguration(newConfig: Partial<ScheduleConfig>): Promise<void> {
    logger.info('Updating scraping configuration...');
    
    // Stop current scheduling
    await this.stopScheduledScraping();
    
    // Update database settings
    for (const [key, value] of Object.entries(newConfig)) {
      await prisma.globalSettings.upsert({
        where: { key: `scraping_${key}` },
        update: { value: typeof value === 'object' ? JSON.stringify(value) : value.toString() },
        create: {
          key: `scraping_${key}`,
          value: typeof value === 'object' ? JSON.stringify(value) : value.toString(),
        }
      });
    }
    
    // Restart with new configuration
    const config = await this.loadConfiguration();
    if (config.enabled) {
      await this.startScheduledScraping(config);
    }
    
    logger.info('Scraping configuration updated');
  }

  getStatus(): { isRunning: boolean; taskCount: number; lastExecution?: Date } {
    return {
      isRunning: this.isRunning,
      taskCount: this.scheduledTasks.size,
    };
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// Export singleton instance
export const scrapingScheduler = new ScrapingScheduler();
