from typing import List, Dict, Any, Optional
import requests

# Helper mappings reused from the Deno function

def map_sport_to_espn(sport: str) -> str:
    mapping = {
        'football': 'football',
        'basketball': 'basketball',
        'baseball': 'baseball',
        'hockey': 'hockey',
        'soccer': 'soccer',
        'tennis': 'tennis'
    }
    return mapping.get(sport.lower(), 'football')


def map_league_to_espn(league: str) -> str:
    mapping = {
        'nfl': 'nfl',
        'nba': 'nba',
        'mlb': 'mlb',
        'nhl': 'nhl',
        'mls': 'mls',
        'epl': 'eng.1'
    }
    return mapping.get(league.lower(), league.lower())


def get_default_league(sport: str) -> str:
    defaults = {
        'football': 'nfl',
        'basketball': 'nba',
        'baseball': 'mlb',
        'hockey': 'nhl',
        'soccer': 'mls'
    }
    return defaults.get(sport.lower(), 'nfl')


def fetch_espn_scoreboard(sport: str, league: Optional[str] = None) -> List[Dict[str, Any]]:
    espn_sport = map_sport_to_espn(sport)
    espn_league = map_league_to_espn(league) if league else get_default_league(sport)

    url = f"https://site.api.espn.com/apis/site/v2/sports/{espn_sport}/{espn_league}/scoreboard"
    try:
        resp = requests.get(url, timeout=15)
        resp.raise_for_status()
        data = resp.json()
    except Exception:
        # Fallback via ScraperAPI if configured
        from app.config import settings
        if not settings.SCRAPERAPI_KEY:
            raise
        from app.scrapers.scraperapi import fetch_via_scraperapi
        r2 = fetch_via_scraperapi(url)
        data = r2.json()

    out: List[Dict[str, Any]] = []
    for event in data.get('events', []):
        comp = (event.get('competitions') or [{}])[0]
        competitors = comp.get('competitors') or []
        home = next((c for c in competitors if c.get('homeAway') == 'home'), None)
        away = next((c for c in competitors if c.get('homeAway') == 'away'), None)
        if not home or not away:
            continue
        out.append({
            'match_id': f"espn_{event.get('id')}",
            'sport': sport,
            'league': league or espn_league,
            'home_team': (home.get('team') or {}).get('displayName') or (home.get('team') or {}).get('name'),
            'away_team': (away.get('team') or {}).get('displayName') or (away.get('team') or {}).get('name'),
            'match_date': event.get('date'),
            'stats': {
                'home_record': ((home.get('records') or [{}])[0] or {}).get('summary', '0-0'),
                'away_record': ((away.get('records') or [{}])[0] or {}).get('summary', '0-0'),
                'home_rating': float((home.get('team') or {}).get('strength') or 75),
                'away_rating': float((away.get('team') or {}).get('strength') or 75),
                'venue': (comp.get('venue') or {}).get('fullName'),
            },
            'source': 'ESPN'
        })
    return out
