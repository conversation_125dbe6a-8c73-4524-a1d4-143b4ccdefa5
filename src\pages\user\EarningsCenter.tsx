import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Gift, Share2, Play, Clock, Zap } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import AdRewardSystem from '@/components/AdRewardSystem';
import ReferralSystem from '@/components/ReferralSystem';
import { Header } from '@/components/Header';

const EarningsCenter: React.FC = () => {
  const { user, profile } = useAuth();
  const [activeTab, setActiveTab] = useState('ads');

  const hasActivePremium = () => {
    return profile?.subscription_status === 'active';
  };

  if (!user) {
    return (
      <div className="min-h-screen bg-background">
        <Header />
        <div className="container mx-auto px-4 py-8">
          <div className="text-center">
            <p className="text-muted-foreground">Please log in to access the earnings center.</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-foreground mb-2">Earnings Center</h1>
          <p className="text-muted-foreground">
            Earn free premium access by watching ads and inviting friends
          </p>
        </div>

        {/* Premium Status Banner */}
        {hasActivePremium() && (
          <Card className="mb-6 bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 border-green-200 dark:border-green-800">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-green-100 dark:bg-green-800 rounded-full">
                  <Zap className="h-5 w-5 text-green-600 dark:text-green-400" />
                </div>
                <div>
                  <h3 className="font-semibold text-green-800 dark:text-green-200">Premium Access Active</h3>
                  <p className="text-sm text-green-600 dark:text-green-300">
                    You have premium access! You can still earn rewards to extend your subscription.
                  </p>
                </div>
                <Badge className="ml-auto bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100">
                  Premium
                </Badge>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Earnings Overview */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">Ways to Earn</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-primary">3</div>
              <p className="text-xs text-muted-foreground mt-1">
                Watch ads, refer friends, complete tasks
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">Max Daily Earnings</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-primary">5 days</div>
              <p className="text-xs text-muted-foreground mt-1">
                Premium access per day
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">Referral Bonus</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-primary">3 days</div>
              <p className="text-xs text-muted-foreground mt-1">
                Per successful referral
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Main Earnings Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="ads" className="flex items-center gap-2">
              <Play className="h-4 w-4" />
              Watch Ads
            </TabsTrigger>
            <TabsTrigger value="referrals" className="flex items-center gap-2">
              <Share2 className="h-4 w-4" />
              Invite Friends
            </TabsTrigger>
          </TabsList>

          <TabsContent value="ads" className="space-y-6">
            <AdRewardSystem />
          </TabsContent>

          <TabsContent value="referrals" className="space-y-6">
            <ReferralSystem />
          </TabsContent>
        </Tabs>

        {/* How It Works */}
        <Card className="mt-8">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Gift className="h-5 w-5" />
              How to Earn Premium Access
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-2 gap-6">
              <div className="space-y-3">
                <h4 className="font-semibold flex items-center gap-2">
                  <Play className="h-4 w-4 text-primary" />
                  Watch Advertisements
                </h4>
                <ul className="space-y-2 text-sm text-muted-foreground ml-6">
                  <li>• Watch 30-second video ads</li>
                  <li>• Earn 1 day premium access per completed ad</li>
                  <li>• Watch up to 5 ads per day</li>
                  <li>• Rewards are instant and stack</li>
                </ul>
              </div>

              <div className="space-y-3">
                <h4 className="font-semibold flex items-center gap-2">
                  <Share2 className="h-4 w-4 text-primary" />
                  Invite Friends
                </h4>
                <ul className="space-y-2 text-sm text-muted-foreground ml-6">
                  <li>• Share your unique referral code</li>
                  <li>• Earn 3 days premium when friends sign up</li>
                  <li>• Your friend gets 1 day welcome bonus</li>
                  <li>• No limit on referrals</li>
                </ul>
              </div>
            </div>

            <div className="mt-6 p-4 bg-muted/50 rounded-lg">
              <h4 className="font-semibold mb-2">Pro Tips 💡</h4>
              <ul className="space-y-1 text-sm text-muted-foreground">
                <li>• Earned premium access days stack on top of existing subscriptions</li>
                <li>• Share your referral code on social media for more sign-ups</li>
                <li>• Check back daily for new earning opportunities</li>
                <li>• Premium access includes all features: advanced predictions, analytics, and priority support</li>
              </ul>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default EarningsCenter;