import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ArrowRight, TrendingUp, Target, Zap, Star } from 'lucide-react';
import { Link } from 'react-router-dom';

const HeroSection: React.FC = () => {
  const [currentStat, setCurrentStat] = useState(0);
  const [animatedNumbers, setAnimatedNumbers] = useState({
    accuracy: 0,
    predictions: 0,
    users: 0
  });

  const stats = [
    { label: "Prediction Accuracy", value: 99.9, suffix: "%" },
    { label: "Daily Predictions", value: 250, suffix: "+" },
    { label: "Active Users", value: 15000, suffix: "+" }
  ];

  const images = [
    {
      src: "/lovable-uploads/74f9ebe9-3442-443d-ad3c-e5046e67a481.png",
      alt: "ML Sports Analysis - Athlete Analytics"
    },
    {
      src: "/lovable-uploads/e0b3facb-aefb-45c5-ba01-7ac22af5283d.png", 
      alt: "ML Sports Predictions - Performance Metrics"
    },
    {
      src: "/lovable-uploads/13794c49-81dc-4bc6-bd91-4a5bc13656b5.png",
      alt: "ML Sports Team - Professional Athletes"
    }
  ];

  const [currentImage, setCurrentImage] = useState(0);

  useEffect(() => {
    // Animate numbers on mount
    const animateNumber = (target: number, key: keyof typeof animatedNumbers) => {
      let current = 0;
      const increment = target / 30;
      const timer = setInterval(() => {
        current += increment;
        if (current >= target) {
          current = target;
          clearInterval(timer);
        }
        setAnimatedNumbers(prev => ({ ...prev, [key]: Math.floor(current) }));
      }, 50);
    };

    animateNumber(99.9, 'accuracy');
    animateNumber(250, 'predictions');
    animateNumber(15000, 'users');

    // Rotate stats display
    const statInterval = setInterval(() => {
      setCurrentStat(prev => (prev + 1) % stats.length);
    }, 3000);

    // Rotate images
    const imageInterval = setInterval(() => {
      setCurrentImage(prev => (prev + 1) % images.length);
    }, 4000);

    return () => {
      clearInterval(statInterval);
      clearInterval(imageInterval);
    };
  }, []);

  return (
    <section className="relative z-0 min-h-screen flex items-center justify-center overflow-hidden bg-background">
      {/* Animated Background Grid */}
      <div className="absolute inset-0 -z-10 pointer-events-none bg-[linear-gradient(rgba(16,185,129,0.1)_1px,transparent_1px),linear-gradient(90deg,rgba(16,185,129,0.1)_1px,transparent_1px)] bg-[size:50px_50px] animate-pulse"></div>
      
      {/* Floating Particles */}
      <div className="absolute inset-0 -z-10 pointer-events-none">
        {[...Array(20)].map((_, i) => (
          <div
            key={i}
            className="absolute w-1 h-1 bg-muted rounded-full animate-ping"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 3}s`,
              animationDuration: `${2 + Math.random() * 2}s`
            }}
          />
        ))}
      </div>

      <div className="container mx-auto px-4 py-20 relative z-10">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Content Side */}
          <div className="space-y-8">
            {/* Badge */}
            <Badge variant="outline" className="bg-card/80 text-primary border-primary/20 backdrop-blur-sm">
              <Star className="w-3 h-3 mr-1" />
              #1 AI Sports Prediction Platform
            </Badge>

            {/* Main Heading */}
            <div className="space-y-4">
              <h1 className="text-4xl md:text-6xl font-bruno font-black leading-tight">
                <span className="text-white border-b-4 border-red-500 pb-2 inline-block">
                  MACHINE LEARNING
                </span>
                <br />
                <span className="text-primary font-bruno drop-shadow-lg">SPORTS PREDICTION ENGINE</span>
              </h1>
            </div>

            {/* Dynamic Stats */}
            <div className="flex flex-wrap gap-6">
              {stats.map((stat, index) => (
                <div 
                  key={index}
                  className={`transition-all duration-500 ${
                    currentStat === index ? 'scale-110 text-primary' : 'text-muted-foreground'
                  }`}
                >
                  <div className="text-3xl font-orbitron font-bold">
                    {index === 0 ? animatedNumbers.accuracy : 
                     index === 1 ? animatedNumbers.predictions : 
                     animatedNumbers.users}{stat.suffix}
                  </div>
                  <div className="text-sm font-rajdhani">{stat.label}</div>
                </div>
              ))}
            </div>

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-4">
              <Button 
                size="lg" 
                className="bg-gradient-primary hover:opacity-90 text-white font-rajdhani font-semibold text-lg px-8 py-6"
                asChild
              >
                <Link to="/predictions">
                  Start Predicting <ArrowRight className="ml-2 w-5 h-5" />
                </Link>
              </Button>
              
              <Button 
                size="lg" 
                variant="outline" 
                className="font-rajdhani font-semibold text-lg px-8 py-6 border-primary/20 hover:bg-primary/10"
                asChild
              >
                <Link to="/about">
                  Learn More
                </Link>
              </Button>
            </div>

            {/* Feature Highlights removed to reduce text */}
          </div>

          {/* Image Side */}
          <div className="flex justify-center lg:justify-end">
            <div className="relative max-w-md w-full">
              {/* Main Image Container */}
              <div className="relative">
                <img 
                  src={images[currentImage].src}
                  alt={images[currentImage].alt}
                  className="w-full h-auto object-contain transition-all duration-1000 transform hover:scale-105"
                />
                
                {/* Glow Effects - Removed red backgrounds */}
                <div className="absolute inset-0 bg-muted/20 blur-3xl scale-110 -z-10 animate-pulse"></div>
                <div className="absolute inset-0 bg-muted/10 blur-2xl scale-105 -z-20 animate-pulse"></div>
              </div>
              
              {/* Floating UI Elements */}
              <div className="absolute -top-4 -right-4 bg-card/90 backdrop-blur-sm border border-primary/20 rounded-xl p-3 animate-bounce">
                <div className="text-2xl font-orbitron font-bold text-primary">99.9%</div>
                <div className="text-xs text-muted-foreground">Accuracy</div>
              </div>
              
              <div className="absolute -bottom-4 -left-4 bg-card/90 backdrop-blur-sm border border-success/20 rounded-xl p-3 animate-pulse">
                <div className="text-2xl font-orbitron font-bold text-success">250+</div>
                <div className="text-xs text-muted-foreground">Daily Picks</div>
              </div>

              {/* Orbit Elements */}
              <div className="absolute inset-0 -z-10">
                {[...Array(3)].map((_, i) => (
                  <div
                    key={i}
                    className={`absolute w-2 h-2 bg-muted/50 rounded-full animate-ping`}
                    style={{
                      left: `${20 + (i * 25)}%`,
                      top: `${10 + (i * 30)}%`,
                      animationDelay: `${i * 0.5}s`,
                      animationDuration: '3s'
                    }}
                  />
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;