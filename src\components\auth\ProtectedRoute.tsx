import { useAuth } from '@/contexts/AuthContext'
import { usePermissions } from '@/hooks/usePermissions'
import { Navigate } from 'react-router-dom'
import LoadingScreen from '@/components/LoadingScreen'

interface ProtectedRouteProps {
  children: React.ReactNode
  requireAuth?: boolean
  requireAdmin?: boolean
  requireSuperAdmin?: boolean
  permission?: keyof ReturnType<typeof usePermissions>
}

export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requireAuth = false,
  requireAdmin = false,
  requireSuperAdmin = false,
  permission,
}) => {
  const { user, loading, isAdmin, isSuperAdmin } = useAuth()
  const permissions = usePermissions()

  if (loading) {
    return <LoadingScreen />
  }

  if (requireAuth && !user) {
    return <Navigate to="/auth/login" replace />
  }

  if (requireAdmin && !isAdmin) {
    return <Navigate to="/unauthorized" replace />
  }

  if (requireSuperAdmin && !isSuperAdmin) {
    return <Navigate to="/unauthorized" replace />
  }

  if (permission && !permissions[permission]) {
    return <Navigate to="/unauthorized" replace />
  }

  return <>{children}</>
}