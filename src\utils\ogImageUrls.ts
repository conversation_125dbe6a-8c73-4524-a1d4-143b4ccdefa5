
export const OG_IMAGES = {
  home: '/og-sports-predictions.jpg',
  predictions: '/og-predictions-dashboard.jpg',
  football: '/og-football-predictions.jpg',
  basketball: '/og-basketball-predictions.jpg',
  tennis: '/og-tennis-predictions.jpg',
  baseball: '/og-baseball-predictions.jpg',
  soccer: '/og-soccer-predictions.jpg',
  default: '/og-sports-predictions.jpg'
} as const;

export const generateDynamicOGUrl = (
  title: string, 
  sport?: string, 
  confidence?: number
): string => {
  const params = new URLSearchParams({
    title: title.substring(0, 100), // Limit title length
    ...(sport && { sport }),
    ...(confidence && { confidence: confidence.toString() })
  });
  
  // This would be your OG image generation service URL
  return `https://1300blk.online/api/og?${params.toString()}`;
};

export const getSportOGImage = (sport: string): string => {
  const sportKey = sport.toLowerCase() as keyof typeof OG_IMAGES;
  return OG_IMAGES[sportKey] || OG_IMAGES.default;
};
