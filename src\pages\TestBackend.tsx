import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { apiService as api } from '@/services/api';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/AuthContext';

const TestBackend: React.FC = () => {
  const { toast } = useToast();
  const { user } = useAuth();
  const [testResults, setTestResults] = useState<any>({});
  const [loading, setLoading] = useState(false);

  const runTest = async (testName: string, testFn: () => Promise<any>) => {
    setLoading(true);
    try {
      const result = await testFn();
      setTestResults(prev => ({
        ...prev,
        [testName]: { success: true, data: result }
      }));
      toast({
        title: `${testName} Test Passed`,
        description: "API call successful",
      });
    } catch (error) {
      setTestResults(prev => ({
        ...prev,
        [testName]: { success: false, error: error.message }
      }));
      toast({
        title: `${testName} Test Failed`,
        description: error.message,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const tests = [
    {
      name: 'Health Check',
      description: 'Test backend health endpoint',
      fn: async () => {
        const response = await fetch('http://localhost:3001/health');
        return await response.json();
      }
    },
    {
      name: 'Get Profile',
      description: 'Test user profile endpoint',
      fn: async () => {
        return await api.getProfile();
      }
    },
    {
      name: 'Get Predictions',
      description: 'Test predictions endpoint',
      fn: async () => {
        return await api.getAllPredictions({ limit: 5 });
      }
    },
    {
      name: 'Get Sports Data',
      description: 'Test sports data endpoint',
      fn: async () => {
        return await api.getSportsData({ limit: 5 });
      }
    },
    {
      name: 'Get Available Sports',
      description: 'Test available sports endpoint',
      fn: async () => {
        return await api.getAvailableSports();
      }
    },
    {
      name: 'Get Prediction Stats',
      description: 'Test prediction statistics endpoint',
      fn: async () => {
        return await api.getPredictionStats();
      }
    },
    {
      name: 'Get Sports Data Stats',
      description: 'Test sports data statistics endpoint',
      fn: async () => {
        return await api.getSportsDataStats();
      }
    },
    {
      name: 'Get User Stats',
      description: 'Test user statistics endpoint (admin only)',
      fn: async () => {
        return await api.getUserStats();
      }
    }
  ];

  const runAllTests = async () => {
    for (const test of tests) {
      await runTest(test.name, test.fn);
      // Small delay between tests
      await new Promise(resolve => setTimeout(resolve, 500));
    }
  };

  return (
    <div className="min-h-screen bg-background p-8">
      <div className="container mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-foreground mb-2">Backend Integration Test</h1>
          <p className="text-muted-foreground">Test all backend API endpoints to verify integration</p>
          {user && (
            <div className="mt-4">
              <Badge variant="outline">
                Logged in as: {user.email} ({user.role})
              </Badge>
            </div>
          )}
        </div>

        <div className="grid gap-6 mb-8">
          <Card>
            <CardHeader>
              <CardTitle>Test Controls</CardTitle>
              <CardDescription>Run individual tests or all tests at once</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex gap-4">
                <Button onClick={runAllTests} disabled={loading}>
                  Run All Tests
                </Button>
                <Button 
                  variant="outline" 
                  onClick={() => setTestResults({})}
                  disabled={loading}
                >
                  Clear Results
                </Button>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Individual Tests</CardTitle>
              <CardDescription>Run specific API endpoint tests</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {tests.map((test) => (
                  <div key={test.name} className="border rounded-lg p-4">
                    <h3 className="font-medium mb-2">{test.name}</h3>
                    <p className="text-sm text-muted-foreground mb-4">{test.description}</p>
                    <div className="flex items-center justify-between">
                      <Button 
                        size="sm" 
                        onClick={() => runTest(test.name, test.fn)}
                        disabled={loading}
                      >
                        Test
                      </Button>
                      {testResults[test.name] && (
                        <Badge 
                          variant={testResults[test.name].success ? 'default' : 'destructive'}
                        >
                          {testResults[test.name].success ? 'Pass' : 'Fail'}
                        </Badge>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Test Results</CardTitle>
              <CardDescription>Detailed results from API tests</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {Object.entries(testResults).map(([testName, result]: [string, any]) => (
                  <div key={testName} className="border rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-medium">{testName}</h4>
                      <Badge variant={result.success ? 'default' : 'destructive'}>
                        {result.success ? 'Success' : 'Failed'}
                      </Badge>
                    </div>
                    <pre className="text-xs bg-muted p-2 rounded overflow-auto max-h-40">
                      {JSON.stringify(result.success ? result.data : result.error, null, 2)}
                    </pre>
                  </div>
                ))}
                {Object.keys(testResults).length === 0 && (
                  <p className="text-muted-foreground text-center py-8">
                    No test results yet. Run some tests to see results here.
                  </p>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default TestBackend;
