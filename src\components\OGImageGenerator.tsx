
import React from 'react';
import { SEOOptimizedImage } from './SEOOptimizedImage';

interface OGImageProps {
  title: string;
  description?: string;
  sport?: string;
  confidence?: number;
  className?: string;
}

export const OGImageGenerator: React.FC<OGImageProps> = ({
  title,
  description,
  sport,
  confidence,
  className = ''
}) => {
  return (
    <div className={`relative w-[1200px] h-[630px] bg-gradient-to-br from-primary via-primary/90 to-secondary overflow-hidden ${className}`}>
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute top-0 left-0 w-full h-full bg-[url('/lovable-uploads/74f9ebe9-3442-443d-ad3c-e5046e67a481.png')] bg-cover bg-center"></div>
      </div>
      
      {/* Grid Pattern Overlay */}
      <div className="absolute inset-0 bg-grid-white/5"></div>
      
      {/* Content Container */}
      <div className="relative z-10 h-full flex flex-col justify-between p-16">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <SEOOptimizedImage
              src="/logo.png"
              alt="ML Sports Intelligent Engine Logo"
              width={80}
              height={80}
              className="rounded-lg"
            />
            <div>
              <h3 className="text-white text-3xl font-bold">ML Sports Engine</h3>
              <p className="text-white/80 text-lg">AI-Powered Predictions</p>
            </div>
          </div>
          
          {sport && (
            <div className="bg-white/20 backdrop-blur-sm rounded-full px-6 py-3">
              <span className="text-white text-xl font-semibold capitalize">{sport}</span>
            </div>
          )}
        </div>
        
        {/* Main Content */}
        <div className="text-center space-y-6">
          <h1 className="text-white text-6xl font-bold leading-tight max-w-4xl mx-auto">
            {title}
          </h1>
          
          {description && (
            <p className="text-white/90 text-2xl max-w-3xl mx-auto leading-relaxed">
              {description}
            </p>
          )}
          
          {confidence && (
            <div className="flex items-center justify-center space-x-4">
              <div className="bg-green-500/20 backdrop-blur-sm rounded-full px-8 py-4">
                <span className="text-green-400 text-2xl font-bold">
                  {confidence}% Confidence
                </span>
              </div>
            </div>
          )}
        </div>
        
        {/* Footer */}
        <div className="flex items-center justify-between">
          <div className="text-white/80 text-lg">
            Advanced ML Algorithms • Real-time Analytics
          </div>
          <div className="text-white/80 text-lg">
            1300blk.online
          </div>
        </div>
      </div>
      
      {/* Decorative Elements */}
      <div className="absolute top-20 right-20 w-32 h-32 bg-white/10 rounded-full blur-xl"></div>
      <div className="absolute bottom-32 left-20 w-24 h-24 bg-primary/30 rounded-full blur-lg"></div>
    </div>
  );
};
