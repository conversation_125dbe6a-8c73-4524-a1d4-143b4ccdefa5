import { PrismaClient } from '@prisma/client';
import { logger } from '@/utils/logger';

declare global {
  var __prisma: PrismaClient | undefined;
}

// Prevent multiple instances of Prisma Client in development
const prisma = globalThis.__prisma || new PrismaClient({
  log: [
    {
      emit: 'event',
      level: 'query',
    },
    {
      emit: 'event',
      level: 'error',
    },
    {
      emit: 'event',
      level: 'info',
    },
    {
      emit: 'event',
      level: 'warn',
    },
  ],
});

if (process.env.NODE_ENV === 'development') {
  globalThis.__prisma = prisma;
}

// Log database queries in development - disabled for SQLite compatibility
// if (process.env.NODE_ENV === 'development') {
//   prisma.$on('query' as any, (e: any) => {
//     logger.debug(`Query: ${e.query}`);
//     logger.debug(`Params: ${e.params}`);
//     logger.debug(`Duration: ${e.duration}ms`);
//   });
// }

// prisma.$on('error' as any, (e: any) => {
//   logger.error('Database error:', e);
// });

// prisma.$on('warn' as any, (e: any) => {
//   logger.warn('Database warning:', e);
// });

// prisma.$on('info' as any, (e: any) => {
//   logger.info('Database info:', e);
// });

export const connectDatabase = async () => {
  try {
    await prisma.$connect();
    logger.info('✅ Database connected successfully');
  } catch (error) {
    logger.error('❌ Database connection failed:', error);
    throw error;
  }
};

export const disconnectDatabase = async () => {
  try {
    await prisma.$disconnect();
    logger.info('✅ Database disconnected successfully');
  } catch (error) {
    logger.error('❌ Database disconnection failed:', error);
    throw error;
  }
};

export { prisma };
export default prisma;
