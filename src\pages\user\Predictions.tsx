import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Calendar, Filter, Star, TrendingUp, Target } from 'lucide-react';

const UserPredictions: React.FC = () => {
  const predictions = [
    {
      id: 1,
      match: "Arsenal vs Chelsea",
      league: "Premier League",
      prediction: "Over 2.5 Goals",
      confidence: 85,
      odds: 1.75,
      date: "2024-01-15",
      status: "Won",
      result: "+$25.00"
    },
    {
      id: 2,
      match: "Barcelona vs Real Madrid",
      league: "La Liga",
      prediction: "Barcelona Win",
      confidence: 72,
      odds: 2.10,
      date: "2024-01-14",
      status: "Lost",
      result: "-$15.00"
    },
    {
      id: 3,
      match: "PSG vs Lyon",
      league: "Ligue 1",
      prediction: "Both Teams to Score",
      confidence: 90,
      odds: 1.55,
      date: "2024-01-13",
      status: "Won",
      result: "+$18.50"
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-hero">
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bruno tracking-wide mb-2">My Predictions</h1>
          <p className="text-muted-foreground">Track your prediction history and performance</p>
        </div>

        {/* Filters */}
        <Card className="bg-gradient-card mb-6">
          <CardContent className="p-4">
            <div className="flex flex-wrap gap-4">
              <Button variant="outline" size="sm">
                <Calendar className="h-4 w-4 mr-2" />
                Date Range
              </Button>
              <Button variant="outline" size="sm">
                <Filter className="h-4 w-4 mr-2" />
                Status
              </Button>
              <Button variant="outline" size="sm">
                <Target className="h-4 w-4 mr-2" />
                League
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Predictions List */}
        <div className="space-y-4">
          {predictions.map((prediction) => (
            <Card key={prediction.id} className="bg-gradient-card">
              <CardContent className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <div>
                    <h3 className="font-semibold text-lg">{prediction.match}</h3>
                    <p className="text-muted-foreground text-sm">{prediction.league}</p>
                  </div>
                  <Badge 
                    variant={prediction.status === 'Won' ? 'default' : 'destructive'}
                    className={prediction.status === 'Won' ? 'bg-success' : ''}
                  >
                    {prediction.status}
                  </Badge>
                </div>
                
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                  <div>
                    <p className="text-sm text-muted-foreground">Prediction</p>
                    <p className="font-medium">{prediction.prediction}</p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Confidence</p>
                    <div className="flex items-center gap-1">
                      <Star className="h-4 w-4 text-warning" />
                      <span className="font-medium">{prediction.confidence}%</span>
                    </div>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Odds</p>
                    <p className="font-medium">{prediction.odds}</p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Result</p>
                    <p className={`font-medium ${prediction.status === 'Won' ? 'text-success' : 'text-destructive'}`}>
                      {prediction.result}
                    </p>
                  </div>
                </div>
                
                <div className="flex items-center justify-between text-sm text-muted-foreground">
                  <span>{prediction.date}</span>
                  <Button variant="ghost" size="sm">View Details</Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </div>
  );
};

export default UserPredictions;