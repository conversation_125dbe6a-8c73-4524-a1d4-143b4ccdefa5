#!/bin/bash

# Deploy Backend to Vercel Script

echo "🚀 Deploying 1300blk Backend to Vercel..."

# Check if vercel CLI is installed
if ! command -v vercel &> /dev/null; then
    echo "❌ Vercel CLI not found. Installing..."
    npm install -g vercel
fi

# Navigate to backend directory
cd backend

echo "📦 Building project..."
npm run build

echo "🔧 Generating Prisma client..."
npx prisma generate

echo "🌐 Deploying to Vercel..."
vercel --prod

echo "✅ Deployment complete!"
echo ""
echo "📋 Next steps:"
echo "1. Copy your Vercel backend URL"
echo "2. Update .env.production with: VITE_API_BASE_URL=https://your-backend-url.vercel.app/api"
echo "3. Set up environment variables in Vercel dashboard"
echo "4. Test your deployment: https://your-backend-url.vercel.app/health"
echo ""
echo "🔗 Environment variables to set in Vercel:"
echo "   - DATABASE_URL"
echo "   - JWT_SECRET"
echo "   - NODE_ENV=production"
echo "   - CORS_ORIGIN=https://1300blk.online"
