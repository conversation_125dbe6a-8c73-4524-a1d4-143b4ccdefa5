import { useState } from "react";
import { Head<PERSON> } from "@/components/Header";
// import { PredictionsTable } from "@/components/PredictionsTable";
import { RealTimePredictions } from "@/components/RealTimePredictions";
import { Card } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Calendar, Filter, SortDesc, Target, TrendingUp } from "lucide-react";
import AthleteAdCard from "@/components/AthleteAdCard";
import DownloadAppSection from "@/components/DownloadAppSection";
import Footer from "@/components/Footer";
import { useIsMobile } from "@/hooks/use-mobile";

const Predictions = () => {
  const [selectedSport, setSelectedSport] = useState('all');
  const [selectedDate, setSelectedDate] = useState('today');
  const isMobile = useIsMobile();

  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      {/* Breadcrumbs */}
      <nav className="container mx-auto px-4 py-4 text-sm text-muted-foreground">
        <ol className="flex items-center space-x-2">
          <li><a href="/" className="hover:text-foreground">Home</a></li>
          <li>/</li>
          <li className="text-foreground">Predictions</li>
        </ol>
      </nav>

      <main className="container mx-auto px-4 pb-8">
        <div className={`grid grid-cols-1 ${isMobile ? '' : 'lg:grid-cols-4'} gap-8`}>
          {/* Main Content */}
          <div className={`${isMobile ? '' : 'lg:col-span-3'} space-y-8`}>
            {/* Removed Hero Section as requested */}

            {/* Quick Stats */}
            <div className={`grid grid-cols-1 ${isMobile ? 'grid-cols-2' : 'md:grid-cols-4'} gap-4 mb-8`}>
              <Card className="p-4 bg-gradient-card border border-border/50">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-muted-foreground">Today's Picks</p>
                    <p className="text-2xl font-bold text-foreground">47</p>
                  </div>
                  <Target className="w-8 h-8 text-primary" />
                </div>
              </Card>
              
              <Card className="p-4 bg-gradient-card border border-border/50">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-muted-foreground">Avg Confidence</p>
                    <p className="text-2xl font-bold text-confidence-high">74.5%</p>
                  </div>
                  <TrendingUp className="w-8 h-8 text-success" />
                </div>
              </Card>

              <Card className="p-4 bg-gradient-card border border-border/50">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-muted-foreground">Win Rate (7d)</p>
                    <p className="text-2xl font-bold text-success">68.2%</p>
                  </div>
                  <div className="w-8 h-8 bg-success/20 rounded-full flex items-center justify-center">
                    <span className="text-sm font-bold text-success">W</span>
                  </div>
                </div>
              </Card>

              <Card className="p-4 bg-gradient-card border border-border/50">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-muted-foreground">High Confidence</p>
                    <p className="text-2xl font-bold text-warning">12</p>
                    <p className="text-xs text-muted-foreground">75%+ confidence</p>
                  </div>
                  <div className="w-8 h-8 bg-warning/20 rounded-full flex items-center justify-center">
                    <span className="text-sm font-bold text-warning">!</span>
                  </div>
                </div>
              </Card>
            </div>

            {/* Filters Section */}
            <Card className="p-6 mb-8 bg-gradient-card border border-border/50">
              <div className={`flex ${isMobile ? 'flex-col gap-4' : 'flex-col lg:flex-row lg:items-center justify-between gap-4'}`}>
                <div className="flex flex-wrap items-center gap-3">
                  <div className="flex items-center space-x-2">
                    <Calendar className="w-4 h-4 text-muted-foreground" />
                    <Select value={selectedDate} onValueChange={setSelectedDate}>
                      <SelectTrigger className="w-[140px]">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="today">Today</SelectItem>
                        <SelectItem value="tomorrow">Tomorrow</SelectItem>
                        <SelectItem value="week">This Week</SelectItem>
                        <SelectItem value="weekend">Weekend</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Filter className="w-4 h-4 text-muted-foreground" />
                    <Select value={selectedSport} onValueChange={setSelectedSport}>
                      <SelectTrigger className="w-[140px]">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Sports</SelectItem>
                        <SelectItem value="football">⚽ Football</SelectItem>
                        <SelectItem value="basketball">🏀 Basketball</SelectItem>
                        <SelectItem value="tennis">🎾 Tennis</SelectItem>
                        <SelectItem value="baseball">⚾ Baseball</SelectItem>
                        <SelectItem value="hockey">🏒 Hockey</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  {!isMobile && (
                    <div className="flex items-center gap-2">
                      <Badge variant="outline" className="cursor-pointer hover:bg-primary hover:text-primary-foreground">
                        High Confidence (75%+)
                      </Badge>
                      <Badge variant="outline" className="cursor-pointer hover:bg-primary hover:text-primary-foreground">
                        Positive Edge
                      </Badge>
                      <Badge variant="outline" className="cursor-pointer hover:bg-primary hover:text-primary-foreground">
                        AI Picks Only
                      </Badge>
                    </div>
                  )}
                </div>

                <div className="flex items-center gap-2">
                  <Button variant="outline" size="sm">
                    <SortDesc className="w-4 h-4 mr-2" />
                    Sort by Confidence
                  </Button>
                </div>
              </div>
            </Card>

            {/* Live Predictions (real-time) */}
            <RealTimePredictions showControls={true} maxPredictions={12} />
            
            {/* SEO & Disclaimer */}
            <div className="mt-12 space-y-6">
              <Card className="p-6 bg-muted/20 border border-border/50">
                <h3 className="text-lg font-semibold text-foreground mb-3">About Our Predictions</h3>
                <div className="prose prose-sm text-muted-foreground max-w-none">
                  <p className="mb-3">
                    Our sports predictions are generated using advanced machine learning algorithms that analyze over 100 
                    factors including team form, head-to-head records, player statistics, injuries, weather conditions, 
                    and historical performance data.
                  </p>
                  <p className="mb-3">
                    Each prediction comes with a confidence score (0-100%) indicating our model's certainty, and an edge 
                    percentage showing the expected value compared to market odds. Higher confidence and positive edge 
                    values indicate stronger predictions.
                  </p>
                  <p className="text-xs text-muted-foreground/80">
                    <strong>Disclaimer:</strong> Sports betting involves risk. Past performance does not guarantee future 
                    results. Please gamble responsibly and only bet what you can afford to lose. 18+ only.
                  </p>
                </div>
              </Card>
            </div>
          </div>

          {/* Sidebar */}
          {!isMobile && (
            <div className="lg:block hidden space-y-6">
              {/* Athlete AI Ad Card */}
              <AthleteAdCard />
              
              {/* Download App Section */}
              <DownloadAppSection />
            </div>
          )}
        </div>
        
        {/* Mobile Cards - positioned after main content on mobile */}
        {isMobile && (
          <div className="mt-8 space-y-6">
            <AthleteAdCard />
            <DownloadAppSection />
          </div>
        )}
      </main>

      <Footer />

      {/* JSON-LD Structured Data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "WebPage",
            "name": "Sports Predictions - AI-Powered Betting Analysis",
            "description": "Professional sports predictions with ML algorithms across 80+ markets including football, basketball, tennis and more.",
            "url": "https://1300blk.online/predictions",
            "breadcrumb": {
              "@type": "BreadcrumbList",
              "itemListElement": [
                {
                  "@type": "ListItem",
                  "position": 1,
                  "name": "Home",
                  "item": "https://1300blk.online/"
                },
                {
                  "@type": "ListItem", 
                  "position": 2,
                  "name": "Predictions",
                  "item": "https://1300blk.online/predictions"
                }
              ]
            },
            "mainEntity": {
              "@type": "Dataset",
              "name": "Sports Predictions Dataset",
              "description": "AI-generated sports betting predictions with confidence scores",
              "keywords": ["sports predictions", "betting analysis", "AI predictions", "confidence scoring"]
            }
          })
        }}
      />
    </div>
  );
};

export default Predictions;