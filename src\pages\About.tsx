import React from 'react';
import { Header } from '@/components/Header';
import <PERSON><PERSON><PERSON>ooter from '@/components/MinimalFooter';
import { SEOHead } from '@/components/SEOHead';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { 
  Brain, 
  Target, 
  TrendingUp, 
  Shield, 
  Users, 
  Award, 
  Zap,
  BarChart3,
  CheckCircle,
  ArrowRight
} from 'lucide-react';
import { Link } from 'react-router-dom';

const About: React.FC = () => {
  const features = [
    {
      icon: <Brain className="w-6 h-6" />,
      title: "Advanced AI Technology",
      description: "Our proprietary machine learning algorithms analyze thousands of data points to deliver predictions with unmatched accuracy."
    },
    {
      icon: <Target className="w-6 h-6" />,
      title: "94% Accuracy Rate",
      description: "Industry-leading prediction accuracy backed by continuous model refinement and real-world performance validation."
    },
    {
      icon: <TrendingUp className="w-6 h-6" />,
      title: "Real-Time Analysis",
      description: "Live data processing ensures our predictions adapt to last-minute changes in team lineups, weather, and market conditions."
    },
    {
      icon: <Shield className="w-6 h-6" />,
      title: "Transparent Results",
      description: "Complete prediction history and performance metrics available to all users. We believe in full transparency."
    },
    {
      icon: <BarChart3 className="w-6 h-6" />,
      title: "80+ Markets Covered",
      description: "Comprehensive coverage across all major sports with predictions for spreads, totals, moneylines, and specialty markets."
    },
    {
      icon: <Zap className="w-6 h-6" />,
      title: "Instant Predictions",
      description: "Get predictions within seconds of games being posted, giving you the competitive edge you need."
    }
  ];

  const stats = [
    { number: "94%", label: "Prediction Accuracy", description: "Verified across all sports" },
    { number: "250+", label: "Daily Predictions", description: "Fresh picks every day" },
    { number: "15K+", label: "Active Users", description: "Trusted by professionals" },
    { number: "80+", label: "Markets Covered", description: "Comprehensive analysis" }
  ];

  const timeline = [
    {
      year: "2020",
      title: "Foundation",
      description: "Started as a research project to apply machine learning to sports analytics."
    },
    {
      year: "2021", 
      title: "AI Development",
      description: "Developed proprietary algorithms and began testing with historical data."
    },
    {
      year: "2022",
      title: "Beta Launch",
      description: "Launched beta version with select users, achieving 85% accuracy rate."
    },
    {
      year: "2023",
      title: "Platform Launch",
      description: "Public launch with 90% accuracy and expanded to multiple sports."
    },
    {
      year: "2024",
      title: "AI Evolution",
      description: "Reached 94% accuracy with advanced neural networks and real-time processing."
    }
  ];

  return (
    <>
      <SEOHead
        title="About 1300BLK AI Sports Engine - Leading AI Sports Prediction Platform"
        description="Learn about 1300BLK AI Sports Engine, the industry-leading platform for AI-powered sports predictions. Discover our technology, mission, and 94% accuracy rate."
        keywords="about 1300BLK AI, sports prediction technology, AI sports analytics, machine learning betting, sports AI platform"
      />

      <div className="min-h-screen bg-gradient-hero">
        <Header />
        
        <main className="container mx-auto px-4 py-16">
          {/* Hero Section */}
          <div className="text-center mb-20">
            <Badge variant="outline" className="mb-6 bg-card/80 text-primary border-primary/20">
              <Award className="w-3 h-3 mr-1" />
              #1 AI Sports Prediction Platform
            </Badge>
            
            <h1 className="text-4xl md:text-6xl font-orbitron font-black mb-6">
              <span className="bg-gradient-primary bg-clip-text text-transparent">
                Revolutionizing Sports
              </span>
              <br />
              <span className="text-foreground">Predictions with AI</span>
            </h1>
            
            <p className="text-xl text-muted-foreground font-exo max-w-4xl mx-auto leading-relaxed">
              At 1300BLK AI, we harness the power of advanced machine learning to deliver 
              the most accurate sports predictions in the industry. Our cutting-edge algorithms 
              analyze millions of data points to give you the competitive edge you need.
            </p>
          </div>

          {/* Stats Grid */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-20">
            {stats.map((stat, index) => (
              <Card key={index} className="bg-card/80 backdrop-blur-sm border-border/50 p-6 text-center hover:border-primary/30 transition-all">
                <div className="text-3xl md:text-4xl font-orbitron font-bold text-primary mb-2">
                  {stat.number}
                </div>
                <div className="font-rajdhani font-semibold text-foreground mb-1">
                  {stat.label}
                </div>
                <div className="text-sm text-muted-foreground">
                  {stat.description}
                </div>
              </Card>
            ))}
          </div>

          {/* Mission Statement */}
          <Card className="bg-gradient-primary/10 border-primary/20 backdrop-blur-sm p-8 md:p-12 mb-20 text-center">
            <h2 className="text-3xl md:text-4xl font-orbitron font-bold mb-6 text-foreground">
              Our Mission
            </h2>
            <p className="text-lg text-muted-foreground font-exo max-w-4xl mx-auto leading-relaxed mb-8">
              To democratize access to professional-grade sports analysis through artificial intelligence. 
              We believe that everyone should have access to the same level of data-driven insights 
              that professional bettors and sports analysts use to make informed decisions.
            </p>
            <div className="flex items-center justify-center gap-2 text-primary">
              <CheckCircle className="w-5 h-5" />
              <span className="font-rajdhani font-semibold">Transparent, Accurate, Reliable</span>
            </div>
          </Card>

          {/* Features Grid */}
          <div className="mb-20">
            <h2 className="text-3xl md:text-4xl font-orbitron font-bold text-center mb-12 text-foreground">
              What Makes Us Different
            </h2>
            
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {features.map((feature, index) => (
                <Card key={index} className="bg-card/80 backdrop-blur-sm border-border/50 p-6 hover:border-primary/30 transition-all group">
                  <div className="p-3 bg-primary/10 rounded-lg w-fit mb-4 group-hover:bg-primary/20 transition-colors">
                    <div className="text-primary">
                      {feature.icon}
                    </div>
                  </div>
                  <h3 className="font-rajdhani font-bold text-xl mb-3 text-foreground">
                    {feature.title}
                  </h3>
                  <p className="text-muted-foreground font-exo leading-relaxed">
                    {feature.description}
                  </p>
                </Card>
              ))}
            </div>
          </div>

          {/* Timeline */}
          <div className="mb-20">
            <h2 className="text-3xl md:text-4xl font-orbitron font-bold text-center mb-12 text-foreground">
              Our Journey
            </h2>
            
            <div className="max-w-4xl mx-auto">
              {timeline.map((item, index) => (
                <div key={index} className="flex gap-6 mb-8 last:mb-0">
                  <div className="flex flex-col items-center">
                    <div className="w-12 h-12 bg-primary/20 border-2 border-primary rounded-full flex items-center justify-center font-orbitron font-bold text-primary text-sm">
                      {item.year}
                    </div>
                    {index < timeline.length - 1 && (
                      <div className="w-0.5 h-16 bg-gradient-to-b from-primary to-transparent mt-2"></div>
                    )}
                  </div>
                  <div className="flex-1 pb-8">
                    <h3 className="font-rajdhani font-bold text-xl mb-2 text-foreground">
                      {item.title}
                    </h3>
                    <p className="text-muted-foreground font-exo">
                      {item.description}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Team Section */}
          <Card className="bg-card/80 backdrop-blur-sm border-border/50 p-8 md:p-12 mb-20 text-center">
            <h2 className="text-3xl md:text-4xl font-orbitron font-bold mb-6 text-foreground">
              Built by Experts
            </h2>
            <p className="text-lg text-muted-foreground font-exo max-w-4xl mx-auto leading-relaxed mb-8">
              Our team consists of data scientists, sports analysts, and machine learning engineers 
              with decades of combined experience in sports betting, artificial intelligence, and 
              financial markets. We're passionate about sports and committed to pushing the 
              boundaries of what's possible with AI.
            </p>
            <div className="flex flex-wrap justify-center gap-4">
              <Badge variant="secondary" className="bg-primary/10 text-primary">
                <Users className="w-3 h-3 mr-1" />
                15+ Team Members
              </Badge>
              <Badge variant="secondary" className="bg-success/10 text-success">
                <Brain className="w-3 h-3 mr-1" />
                PhD Data Scientists
              </Badge>
              <Badge variant="secondary" className="bg-info/10 text-info">
                <Award className="w-3 h-3 mr-1" />
                Industry Veterans
              </Badge>
            </div>
          </Card>

          {/* CTA Section */}
          <Card className="bg-gradient-primary/10 border-primary/20 backdrop-blur-sm p-8 text-center">
            <h3 className="text-2xl md:text-3xl font-orbitron font-bold mb-4 text-foreground">
              Ready to Experience the Future?
            </h3>
            <p className="text-muted-foreground font-exo mb-6 max-w-2xl mx-auto">
              Join thousands of successful bettors who trust 1300BLK AI for their sports predictions. 
              Start your journey with our industry-leading AI platform today.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button 
                size="lg" 
                className="bg-gradient-primary hover:opacity-90 text-white font-rajdhani font-semibold"
                asChild
              >
                <Link to="/predictions">
                  Start Predicting <ArrowRight className="ml-2 w-4 h-4" />
                </Link>
              </Button>
              <Button 
                size="lg" 
                variant="outline" 
                className="font-rajdhani font-semibold border-primary/20 hover:bg-primary/10"
                asChild
              >
                <Link to="/faq">
                  Learn More
                </Link>
              </Button>
            </div>
          </Card>
        </main>

        <MinimalFooter />
      </div>
    </>
  );
};

export default About;