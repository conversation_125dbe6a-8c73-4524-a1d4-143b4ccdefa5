# Complete Scrape & Predict System Guide

## 🚀 Quick Start

Deploy the edge functions:
```powershell
.\deploy-scrape-predict.ps1 -Test
```

## 📁 File Structure

```
supabase/functions/
├── scrape-and-predict/
│   ├── index.ts              # Main edge function
│   ├── enhanced-features.ts  # Advanced utilities
│   ├── deno.json            # Deno configuration
│   └── README.md            # Function documentation
├── monitoring-dashboard/
│   └── index.ts             # Monitoring dashboard
.github/workflows/
└── deploy-edge-functions.yml # CI/CD pipeline
```

## 🎯 Core Features

### Scrape and Predict Function
- **Multi-source scraping**: Process multiple URLs simultaneously
- **Intelligent extraction**: JSON-LD, DOM parsing, regex patterns
- **Auto predictions**: Generate ML predictions from scraped data
- **Python ML integration**: Connect to your existing ML service
- **Rate limiting**: 20 requests per minute per user
- **Caching**: 5-minute TTL for repeated requests
- **Retry logic**: Exponential backoff with 3 attempts

### Monitoring Dashboard
- **Real-time metrics**: Scraping performance, prediction accuracy
- **Visual dashboard**: HTML interface at `/monitoring-dashboard?format=html`
- **Alert system**: Error tracking and notifications
- **Performance monitoring**: Execution times, cache hit rates

## 🔧 Configuration

### Environment Variables
```bash
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
PY_SERVICE_URL=https://your-python-service.com
```

### Request Examples

**Basic scraping with predictions:**
```json
{
  "urls": ["https://espn.com/nfl/schedule"],
  "sport": "football",
  "league": "NFL",
  "auto_predict": true
}
```

**Advanced configuration:**
```json
{
  "urls": ["https://sportsbook.com/games"],
  "sport": "basketball",
  "league": "NBA",
  "auto_predict": true,
  "prediction_markets": ["Moneyline", "Spread", "Over/Under"],
  "scrape_config": {
    "selectors": {
      "matches": ".game-row",
      "teams": ".team-name",
      "odds": ".odds-value",
      "dates": ".game-date"
    },
    "filters": {
      "min_confidence": 70,
      "max_matches": 25
    }
  }
}
```

## 📊 API Endpoints

### POST /functions/v1/scrape-and-predict
Main function for scraping and prediction generation.

**Headers:**
- `Authorization: Bearer <admin-jwt-token>`
- `Content-Type: application/json`

**Response:**
```json
{
  "success": true,
  "scraped_matches": 15,
  "saved_matches": 12,
  "predictions_generated": 8,
  "saved_predictions": 8,
  "data": {
    "scraped_matches": [...],
    "predictions": [...]
  }
}
```

### GET /functions/v1/monitoring-dashboard
Monitoring dashboard with metrics and alerts.

**Query Parameters:**
- `format=html` - Returns HTML dashboard
- `format=json` - Returns JSON metrics (default)

## 🔒 Security

- **Admin authentication**: Requires `admin` or `super_admin` role
- **Rate limiting**: 20 requests/minute per user
- **CORS configured**: Proper headers for web access
- **Audit logging**: All operations logged to `audit_logs` table

## 🚀 Deployment

### Manual Deployment
```powershell
# Deploy with testing
.\deploy-scrape-predict.ps1 -Test

# Force deployment (skip JWT verification)
.\deploy-scrape-predict.ps1 -Force

# Deploy to specific environment
.\deploy-scrape-predict.ps1 -Environment staging
```

### Automated CI/CD
Push to `main` branch triggers production deployment.
Push to `develop` branch triggers staging deployment.

### GitHub Secrets Required
```
SUPABASE_ACCESS_TOKEN
SUPABASE_PROJECT_ID
SUPABASE_URL
SUPABASE_SERVICE_ROLE_KEY
PY_SERVICE_URL
```

## 🧪 Testing

### Local Testing
```bash
# Test the function locally
node test-scrape-predict.js
```

### Production Testing
```bash
curl -X POST https://your-project.supabase.co/functions/v1/scrape-and-predict \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"sport": "football", "league": "NFL", "auto_predict": true}'
```

## 📈 Monitoring

### Dashboard Access
- JSON: `GET /functions/v1/monitoring-dashboard`
- HTML: `GET /functions/v1/monitoring-dashboard?format=html`

### Key Metrics
- **Scraping**: Success rate, response times, sources
- **Predictions**: Generation count, accuracy, confidence distribution
- **Performance**: Function calls, execution time, error rate
- **Alerts**: Recent errors and warnings

## 🔧 Troubleshooting

### Common Issues

**Authentication Errors:**
- Verify JWT token is valid admin token
- Check user has `admin` or `super_admin` role

**Scraping Failures:**
- URLs may be blocking requests
- Check network connectivity
- Verify selectors match target site structure

**Prediction Errors:**
- Python service may be unavailable
- Fallback prediction engine will activate
- Check `PY_SERVICE_URL` configuration

**Rate Limiting:**
- Wait 1 minute between high-volume requests
- Implement request queuing for batch operations

### Debug Mode
Add `"debug": true` to request payload for verbose logging.

## 🎯 Best Practices

1. **Respectful Scraping**: Built-in delays between requests
2. **Error Handling**: Graceful fallbacks and retries
3. **Monitoring**: Regular dashboard checks
4. **Testing**: Validate functions after deployment
5. **Security**: Never expose admin tokens in client code

## 📚 Integration Examples

### Frontend Integration
```javascript
const response = await fetch('/functions/v1/scrape-and-predict', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${adminToken}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    urls: selectedUrls,
    sport: currentSport,
    auto_predict: true
  })
});
```

### Scheduled Automation
Set up cron jobs or scheduled functions to run scraping automatically:
```sql
SELECT cron.schedule('scrape-daily', '0 6 * * *', 
  'SELECT net.http_post(url := ''https://your-project.supabase.co/functions/v1/scrape-and-predict'', 
                        headers := ''{"Authorization": "Bearer token"}'', 
                        body := ''{"auto_predict": true}'')'
);
```

## 🆘 Support

For issues or questions:
1. Check the monitoring dashboard for alerts
2. Review audit logs in Supabase dashboard
3. Test with minimal request payload
4. Verify environment variables are set correctly
