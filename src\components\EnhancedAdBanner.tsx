import React, { useState, useEffect } from 'react';
import { cn } from '@/lib/utils';
import { X, ExternalLink, Target } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

interface AdBannerProps {
  position: 'header' | 'sidebar' | 'inline' | 'footer' | 'modal';
  className?: string;
  autoClose?: boolean;
  closeDelay?: number;
  showPerformanceMetrics?: boolean;
}

interface AdData {
  id: string;
  title: string;
  description: string;
  imageUrl?: string;
  targetUrl: string;
  impressions: number;
  clicks: number;
  ctr: number; // Click-through rate
}

// Mock ad data - in production, this would come from an ad network
const mockAds: Record<string, AdData[]> = {
  header: [
    {
      id: 'h1',
      title: 'Premium Sports Analytics',
      description: 'Unlock advanced AI predictions with 99.9% accuracy',
      imageUrl: '/lovable-uploads/74f9ebe9-3442-443d-ad3c-e5046e67a481.png',
      targetUrl: '/user/subscription',
      impressions: 12543,
      clicks: 892,
      ctr: 7.1
    }
  ],
  sidebar: [
    {
      id: 's1',
      title: 'Crypto Betting',
      description: 'Pay with Bitcoin & Ethereum for instant transactions',
      targetUrl: '/user/subscription',
      impressions: 8932,
      clicks: 445,
      ctr: 4.98
    }
  ],
  inline: [
    {
      id: 'i1',
      title: 'Mobile App Available',
      description: 'Get predictions on the go with our mobile app',
      targetUrl: '#download',
      impressions: 15678,
      clicks: 1234,
      ctr: 7.87
    }
  ],
  footer: [
    {
      id: 'f1',
      title: 'Partner with 1300BLK',
      description: 'Join our affiliate program and earn commission',
      targetUrl: '/contact',
      impressions: 5432,
      clicks: 123,
      ctr: 2.26
    }
  ],
  modal: [
    {
      id: 'm1',
      title: 'Limited Time Offer',
      description: '50% off Premium subscription - Upgrade now!',
      targetUrl: '/user/subscription',
      impressions: 3456,
      clicks: 567,
      ctr: 16.4
    }
  ]
};

export const EnhancedAdBanner: React.FC<AdBannerProps> = ({
  position,
  className,
  autoClose = false,
  closeDelay = 10000,
  showPerformanceMetrics = false
}) => {
  const [isVisible, setIsVisible] = useState(true);
  const [currentAdIndex, setCurrentAdIndex] = useState(0);
  const [isHovered, setIsHovered] = useState(false);
  
  const ads = mockAds[position] || [];
  const currentAd = ads[currentAdIndex];

  useEffect(() => {
    if (autoClose && !isHovered) {
      const timer = setTimeout(() => {
        setIsVisible(false);
      }, closeDelay);

      return () => clearTimeout(timer);
    }
  }, [autoClose, closeDelay, isHovered]);

  useEffect(() => {
    if (ads.length > 1) {
      const interval = setInterval(() => {
        setCurrentAdIndex(prev => (prev + 1) % ads.length);
      }, 5000);

      return () => clearInterval(interval);
    }
  }, [ads.length]);

  const handleClick = (ad: AdData) => {
    // Track click analytics
    console.log(`Ad clicked: ${ad.id} - ${ad.title}`);
    
    // Update CTR (in production, this would be sent to analytics service)
    ad.clicks += 1;
    ad.ctr = (ad.clicks / ad.impressions) * 100;
    
    // Navigate to target URL
    if (ad.targetUrl.startsWith('http')) {
      window.open(ad.targetUrl, '_blank');
    } else {
      window.location.href = ad.targetUrl;
    }
  };

  const trackImpression = (ad: AdData) => {
    // Track impression analytics
    console.log(`Ad impression: ${ad.id} - ${ad.title}`);
    ad.impressions += 1;
  };

  useEffect(() => {
    if (currentAd && isVisible) {
      trackImpression(currentAd);
    }
  }, [currentAd, isVisible]);

  if (!isVisible || !currentAd) {
    return null;
  }

  const getSizeClasses = () => {
    switch (position) {
      case 'header':
        return 'h-20 w-full max-w-6xl mx-auto';
      case 'sidebar':
        return 'h-64 w-full max-w-xs';
      case 'inline':
        return 'h-32 w-full max-w-2xl mx-auto';
      case 'footer':
        return 'h-16 w-full max-w-4xl mx-auto';
      case 'modal':
        return 'h-48 w-full max-w-md';
      default:
        return 'h-32 w-full';
    }
  };

  return (
    <div 
      className={cn(
        "relative bg-gradient-card border border-muted/30 rounded-lg overflow-hidden group transition-all duration-300 hover:shadow-glow",
        getSizeClasses(),
        className
      )}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Close Button */}
      <Button
        variant="ghost"
        size="sm"
        className="absolute top-1 right-1 z-10 opacity-0 group-hover:opacity-100 transition-opacity"
        onClick={() => setIsVisible(false)}
      >
        <X className="h-3 w-3" />
      </Button>

      {/* Ad Content */}
      <div 
        className="h-full w-full cursor-pointer flex items-center p-4"
        onClick={() => handleClick(currentAd)}
      >
        <div className="flex items-center gap-4 w-full">
          {/* Ad Image */}
          {currentAd.imageUrl && (
            <div className="flex-shrink-0">
              <img 
                src={currentAd.imageUrl} 
                alt={currentAd.title}
                className="w-12 h-12 object-cover rounded"
              />
            </div>
          )}

          {/* Ad Text */}
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2">
              <h3 className="font-medium text-sm text-foreground truncate">
                {currentAd.title}
              </h3>
              <Badge variant="outline" className="text-xs">Ad</Badge>
            </div>
            <p className="text-xs text-muted-foreground line-clamp-2">
              {currentAd.description}
            </p>
            
            {/* Performance Metrics */}
            {showPerformanceMetrics && (
              <div className="flex items-center gap-3 mt-1 text-xs text-muted-foreground">
                <span>{currentAd.impressions.toLocaleString()} views</span>
                <span>{currentAd.clicks.toLocaleString()} clicks</span>
                <span>{currentAd.ctr.toFixed(1)}% CTR</span>
              </div>
            )}
          </div>

          {/* CTA Icon */}
          <div className="flex-shrink-0">
            <ExternalLink className="h-4 w-4 text-muted-foreground group-hover:text-primary transition-colors" />
          </div>
        </div>
      </div>

      {/* Multiple Ads Indicator */}
      {ads.length > 1 && (
        <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 flex gap-1">
          {ads.map((_, index) => (
            <div
              key={index}
              className={cn(
                "w-1.5 h-1.5 rounded-full transition-colors",
                index === currentAdIndex ? "bg-primary" : "bg-muted-foreground/30"
              )}
            />
          ))}
        </div>
      )}

      {/* Hover Effects */}
      <div className="absolute inset-0 bg-gradient-to-r from-primary/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none" />
    </div>
  );
};

// Specific Ad Components for easy usage
export const AdSenseHeaderEnhanced: React.FC<{ className?: string }> = ({ className }) => (
  <EnhancedAdBanner position="header" className={className} />
);

export const AdSenseSidebarEnhanced: React.FC<{ className?: string }> = ({ className }) => (
  <EnhancedAdBanner position="sidebar" className={className} />
);

export const AdSenseInlineEnhanced: React.FC<{ className?: string }> = ({ className }) => (
  <EnhancedAdBanner position="inline" className={className} />
);

export const AdSenseFooterEnhanced: React.FC<{ className?: string }> = ({ className }) => (
  <EnhancedAdBanner position="footer" className={className} />
);

export const AdSenseModalEnhanced: React.FC<{ className?: string }> = ({ className }) => (
  <EnhancedAdBanner 
    position="modal" 
    className={className} 
    autoClose={true} 
    closeDelay={15000}
    showPerformanceMetrics={true}
  />
);