
import { createRoot } from 'react-dom/client'
import { StrictMode } from 'react'
import App from './App.tsx'
import './index.css'
import { HelmetProvider } from 'react-helmet-async'
import { Analytics } from '@vercel/analytics/react'
import ConsentBanner from '@/components/privacy/ConsentBanner'

console.log('Starting app render...');

const helmetContext = {};

createRoot(document.getElementById("root")!).render(
  <StrictMode>
    <HelmetProvider context={helmetContext}>
      <App />
      <ConsentBanner />
      <Analytics />
    </HelmetProvider>
  </StrictMode>
);
