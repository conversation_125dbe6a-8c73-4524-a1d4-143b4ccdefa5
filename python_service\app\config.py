from pydantic_settings import BaseSettings
from pydantic import HttpUrl
from typing import Optional


class Settings(BaseSettings):
    SUPABASE_URL: str
    SUPABASE_SERVICE_ROLE_KEY: str
    SUPABASE_ANON_KEY: Optional[str] = None
    SUPABASE_SCHEMA: str = "public"

    PROXY_URL: Optional[str] = None
    SCRAPERAPI_KEY: Optional[str] = None
    REQUEST_TIMEOUT_SECONDS: int = 20
    USER_AGENT: str = (
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 "
        "(KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
    )

    MODEL_DIR: str = "./models"

    class Config:
        env_file = ".env"
        extra = "ignore"


settings = Settings()
