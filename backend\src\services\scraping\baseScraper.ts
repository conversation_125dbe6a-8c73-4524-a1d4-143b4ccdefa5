import axios, { AxiosRequestConfig } from 'axios';
import { logger } from '@/utils/logger';

export interface ScrapingResult {
  success: boolean;
  data?: any[];
  error?: string;
  source: string;
  timestamp: Date;
  matchesFound: number;
}

export interface MatchData {
  matchId?: string;
  externalId?: string;
  sport: string;
  league: string;
  homeTeam: string;
  awayTeam: string;
  matchDate: Date;
  odds?: {
    moneyline?: { home: number; away: number };
    spread?: { home: number; away: number; line: number };
    total?: { over: number; under: number; line: number };
  };
  stats?: {
    home_rating?: number;
    away_rating?: number;
    home_offense_rating?: number;
    away_offense_rating?: number;
    home_defense_rating?: number;
    away_defense_rating?: number;
    [key: string]: any;
  };
  status?: string;
  homeScore?: number;
  awayScore?: number;
  source: string;
}

export abstract class BaseScraper {
  protected name: string;
  protected baseUrl: string;
  protected apiKey?: string;
  protected timeout: number;
  protected retryAttempts: number;
  protected retryDelay: number;

  constructor(name: string, baseUrl: string, apiKey?: string) {
    this.name = name;
    this.baseUrl = baseUrl;
    this.apiKey = apiKey;
    this.timeout = parseInt(process.env.REQUEST_TIMEOUT_SECONDS || '20') * 1000;
    this.retryAttempts = 3;
    this.retryDelay = 1000;
  }

  protected async makeRequest(url: string, config?: AxiosRequestConfig): Promise<any> {
    const requestConfig: AxiosRequestConfig = {
      timeout: this.timeout,
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        ...config?.headers,
      },
      ...config,
    };

    if (this.apiKey) {
      requestConfig.headers = {
        ...requestConfig.headers,
        'Authorization': `Bearer ${this.apiKey}`,
      };
    }

    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= this.retryAttempts; attempt++) {
      try {
        logger.debug(`${this.name}: Making request to ${url} (attempt ${attempt})`);
        const response = await axios.get(url, requestConfig);
        return response.data;
      } catch (error) {
        lastError = error as Error;
        logger.warn(`${this.name}: Request failed (attempt ${attempt}): ${lastError.message}`);
        
        if (attempt < this.retryAttempts) {
          await this.delay(this.retryDelay * attempt);
        }
      }
    }

    throw lastError;
  }

  protected delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  protected normalizeTeamName(teamName: string): string {
    return teamName
      .trim()
      .replace(/\s+/g, ' ')
      .replace(/[^\w\s]/g, '')
      .toLowerCase()
      .split(' ')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  }

  protected generateMatchId(homeTeam: string, awayTeam: string, matchDate: Date, league: string): string {
    const dateStr = matchDate.toISOString().split('T')[0];
    const home = homeTeam.toLowerCase().replace(/\s+/g, '_');
    const away = awayTeam.toLowerCase().replace(/\s+/g, '_');
    const leagueStr = league.toLowerCase().replace(/\s+/g, '_');
    return `${leagueStr}_${dateStr}_${home}_${away}`;
  }

  protected validateMatchData(match: MatchData): boolean {
    return !!(
      match.sport &&
      match.league &&
      match.homeTeam &&
      match.awayTeam &&
      match.matchDate &&
      match.source
    );
  }

  abstract scrapeMatches(sport: string, league?: string): Promise<ScrapingResult>;

  protected createResult(
    success: boolean,
    data?: MatchData[],
    error?: string
  ): ScrapingResult {
    return {
      success,
      data,
      error,
      source: this.name,
      timestamp: new Date(),
      matchesFound: data?.length || 0,
    };
  }
}
