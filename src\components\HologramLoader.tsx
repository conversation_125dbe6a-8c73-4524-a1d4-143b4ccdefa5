import React from 'react';

const HologramLoader = () => {
  return (
    <div className="fixed inset-0 bg-background z-50 flex items-center justify-center">
      <div className="relative">
        {/* Nebula Background */}
        <div className="absolute w-[120%] h-[120%] -top-[10%] -left-[10%] opacity-50">
          <div 
            className="w-full h-full animate-pulse"
            style={{
              background: `
                radial-gradient(ellipse at 30% 30%, rgba(63, 0, 113, 0.3) 0%, rgba(63, 0, 113, 0) 70%),
                radial-gradient(ellipse at 70% 60%, rgba(0, 113, 167, 0.3) 0%, rgba(0, 113, 167, 0) 70%),
                radial-gradient(ellipse at 50% 50%, rgba(167, 0, 157, 0.2) 0%, rgba(167, 0, 157, 0) 70%)
              `,
              filter: 'blur(30px)',
              animation: 'nebula-shift 30s infinite alternate ease-in-out'
            }}
          />
        </div>

        {/* Grid Plane */}
        <div 
          className="absolute w-[200%] h-[200%] -top-[50%] -left-[50%] opacity-30"
          style={{
            backgroundImage: `
              linear-gradient(rgba(0, 162, 255, 0.15) 1px, transparent 1px),
              linear-gradient(90deg, rgba(0, 162, 255, 0.15) 1px, transparent 1px)
            `,
            backgroundSize: '40px 40px',
            transform: 'perspective(500px) rotateX(60deg)',
            transformOrigin: 'center',
            animation: 'grid-move 20s linear infinite'
          }}
        />

        {/* Main Loader Container */}
        <div className="relative w-96 h-96 flex justify-center items-center" style={{ perspective: '800px' }}>
          
          {/* Hologram Platform */}
          <div 
            className="absolute w-72 h-16 -bottom-20 rounded-full opacity-60"
            style={{
              background: 'radial-gradient(ellipse, rgba(0, 221, 255, 0.3) 0%, rgba(0, 0, 0, 0) 70%)',
              boxShadow: '0 0 30px rgba(0, 221, 255, 0.4)',
              transform: 'rotateX(60deg)',
              filter: 'blur(8px)',
              animation: 'platform-glow 4s infinite alternate'
            }}
          />

          {/* Platform Rings */}
          <div className="absolute w-72 h-16 -bottom-20" style={{ transform: 'rotateX(60deg)' }}>
            {[1, 2, 3].map((i) => (
              <div
                key={i}
                className="absolute border-2 rounded-full opacity-40"
                style={{
                  width: `${100 - (i-1)*15}%`,
                  height: `${100 - (i-1)*15}%`,
                  top: `${(i-1)*7.5}%`,
                  left: `${(i-1)*7.5}%`,
                  borderColor: i % 2 === 1 ? 'rgba(0, 221, 255, 0.4)' : 'rgba(255, 0, 255, 0.4)',
                  animation: `platform-ring-pulse 4s infinite alternate`,
                  animationDelay: `-${i}s`
                }}
              />
            ))}
          </div>

          {/* Projection Beams */}
          <div className="absolute w-72 h-72 -bottom-20 opacity-30 pointer-events-none">
            {[1, 2, 3, 4].map((i) => (
              <div
                key={i}
                className="absolute w-px h-64 bottom-0"
                style={{
                  left: `${20 + i*15}%`,
                  background: 'linear-gradient(0deg, rgba(0, 221, 255, 0.8) 0%, rgba(255, 255, 255, 0.4) 40%, rgba(0, 0, 0, 0) 100%)',
                  transform: `rotateY(${i % 2 === 0 ? '-' : ''}${5 + i*2}deg) rotateX(-${25 + i*2}deg)`,
                  transformOrigin: 'bottom',
                  filter: 'blur(1px)',
                  opacity: 0.7,
                  animation: `beam-flicker ${3 + i*0.5}s infinite alternate`,
                  animationDelay: `${i*0.5}s`
                }}
              />
            ))}
          </div>

          {/* Main Holo Container */}
          <div 
            className="relative w-48 h-48"
            style={{
              transformStyle: 'preserve-3d',
              animation: 'float-container 6s infinite ease-in-out'
            }}
          >
            {/* Holo Sphere */}
            <div 
              className="relative w-44 h-44 ml-4 mt-4"
              style={{
                transformStyle: 'preserve-3d',
                animation: 'rotate 15s infinite linear'
              }}
            >
              {/* Holo Rings */}
              {[1, 2, 3, 4, 5].map((i) => (
                <div
                  key={i}
                  className="absolute border-2 rounded-full"
                  style={{
                    width: i === 4 ? '90%' : i === 5 ? '110%' : '100%',
                    height: i === 4 ? '90%' : i === 5 ? '110%' : '100%',
                    top: i === 4 ? '5%' : i === 5 ? '-5%' : '0',
                    left: i === 4 ? '5%' : i === 5 ? '-5%' : '0',
                    borderColor: i % 2 === 1 ? '#ff00de' : '#00ddff',
                    borderWidth: '2px',
                    borderStyle: 'solid',
                    borderTopColor: i % 2 === 1 ? '#ff00de' : '#00ddff',
                    borderBottomColor: i % 2 === 1 ? '#00ddff' : '#ff00de',
                    boxShadow: '0 0 20px rgba(255, 0, 222, 0.5), 0 0 20px rgba(0, 221, 255, 0.5)',
                    filter: 'blur(1px)',
                    animation: 'pulse 3s infinite ease-in-out alternate',
                    animationDelay: `-${i*0.5}s`,
                    transform: i === 2 ? 'rotateX(60deg)' : i === 3 ? 'rotateY(60deg)' : i === 4 ? 'rotateZ(45deg)' : i === 5 ? 'rotateX(30deg) rotateY(30deg)' : 'none'
                  }}
                />
              ))}

              {/* Holo Particles */}
              <div className="absolute w-full h-full">
                {[1, 2, 3, 4, 5, 6, 7, 8].map((i) => (
                  <div
                    key={i}
                    className="absolute rounded-full opacity-0"
                    style={{
                      width: `${4 + i*2}px`,
                      height: `${4 + i*2}px`,
                      top: `${20 + i*10}%`,
                      left: `${30 + i*5}%`,
                      background: 'radial-gradient(circle, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0) 70%)',
                      filter: 'blur(1px)',
                      animation: 'particle-float 4s infinite ease-in-out',
                      animationDelay: `${i*0.3}s`
                    }}
                  />
                ))}
              </div>
            </div>

            {/* Core */}
            <div 
              className="absolute w-12 h-12 top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 rounded-full z-10"
              style={{
                background: 'radial-gradient(circle, #ffffff 0%, rgba(255, 255, 255, 0.8) 30%, rgba(255, 255, 255, 0) 100%)',
                filter: 'blur(5px)',
                boxShadow: '0 0 20px rgba(255, 255, 255, 0.8), 0 0 40px rgba(0, 221, 255, 0.6), 0 0 60px rgba(255, 0, 222, 0.4)',
                animation: 'core-pulse 1.5s infinite ease-in-out alternate'
              }}
            />
          </div>

          {/* Code Lines */}
          <div className="absolute w-[200%] h-[200%] -top-[50%] -left-[50%] pointer-events-none z-20">
            {[
              '01001001 01001110 01001001 01010100 01001001 01000001 01001100 01001001',
              'function initHolographicMatrix() { connectNodes(); renderQuantumState(); }',
              '01010011 01011001 01010011 01010100 01000101 01001101 00100000 01001100',
              'class QuantumProcessor { constructor() { this.entanglement = new Map(); }}',
              'const matrix = [1.2, 0.8, 3.1, 2.7, 5.9, 4.3, 7.2, 9.0];',
              'async function loadHolographicData() { await fetch(\'/api/quantum\'); }'
            ].map((code, i) => (
              <div
                key={i}
                className="absolute text-xs whitespace-nowrap opacity-0"
                style={{
                  top: `${10 + i*15}%`,
                  color: i % 2 === 0 ? '#00ddff' : '#ff00de',
                  textShadow: '0 0 5px rgba(0, 221, 255, 0.3)',
                  animation: 'code-scroll 15s infinite linear',
                  animationDelay: `-${i}s`
                }}
              >
                {code}
              </div>
            ))}
          </div>

          {/* Corner Decorations */}
          <div className="absolute w-72 h-72 pointer-events-none">
            {[1, 2, 3, 4].map((i) => (
              <div
                key={i}
                className="absolute w-5 h-5 border-2"
                style={{
                  top: i <= 2 ? 0 : 'auto',
                  bottom: i > 2 ? 0 : 'auto',
                  left: i % 2 === 1 ? 0 : 'auto',
                  right: i % 2 === 0 ? 0 : 'auto',
                  borderColor: 'rgba(0, 221, 255, 0.5)',
                  borderRightStyle: i % 2 === 1 ? 'none' : 'solid',
                  borderLeftStyle: i % 2 === 0 ? 'none' : 'solid',
                  borderBottomStyle: i <= 2 ? 'none' : 'solid',
                  borderTopStyle: i > 2 ? 'none' : 'solid',
                  animation: 'corner-pulse 3s infinite alternate'
                }}
              />
            ))}
          </div>

          {/* Loading Text */}
          <div 
            className="absolute -bottom-24 text-center text-sm tracking-widest"
            style={{
              color: 'rgba(255, 255, 255, 0.8)',
              textShadow: '0 0 10px rgba(0, 221, 255, 0.6)',
              animation: 'text-flicker 2s infinite'
            }}
          >
            SYSTEM INITIALIZATION
          </div>

          {/* Progress Bar */}
          <div className="absolute -bottom-28 w-48 h-1 bg-white/10 rounded-full overflow-hidden">
            <div 
              className="h-full w-0 rounded-full"
              style={{
                background: 'linear-gradient(90deg, #00ddff, #ff00de)',
                boxShadow: '0 0 10px rgba(0, 221, 255, 0.7)',
                animation: 'progress 5s linear infinite'
              }}
            />
          </div>
        </div>
      </div>

      <style>{`
        @keyframes nebula-shift {
          0% { transform: scale(1) rotate(0deg); opacity: 0.3; }
          50% { opacity: 0.5; }
          100% { transform: scale(1.2) rotate(5deg); opacity: 0.4; }
        }
        
        @keyframes grid-move {
          0% { transform: perspective(500px) rotateX(60deg) translateY(0); }
          100% { transform: perspective(500px) rotateX(60deg) translateY(40px); }
        }
        
        @keyframes platform-glow {
          0% { box-shadow: 0 0 30px rgba(0, 221, 255, 0.4); }
          100% { box-shadow: 0 0 50px rgba(255, 0, 255, 0.6); }
        }
        
        @keyframes platform-ring-pulse {
          0% { transform: scale(1); opacity: 0.2; }
          100% { transform: scale(1.05); opacity: 0.6; }
        }
        
        @keyframes beam-flicker {
          0%, 100% { opacity: 0.3; }
          50% { opacity: 0.7; }
        }
        
        @keyframes float-container {
          0%, 100% { transform: translateY(0); }
          50% { transform: translateY(-20px); }
        }
        
        @keyframes rotate {
          0% { transform: rotateX(0deg) rotateY(0deg) rotateZ(0deg); }
          100% { transform: rotateX(360deg) rotateY(360deg) rotateZ(360deg); }
        }
        
        @keyframes pulse {
          0%, 100% { opacity: 0.8; }
          50% { opacity: 1; }
        }
        
        @keyframes particle-float {
          0%, 100% { transform: translateZ(0) translateX(0) translateY(0) scale(0.8); opacity: 0; }
          25% { opacity: 1; transform: translateZ(30px) translateX(10px) translateY(-10px) scale(1); }
          50% { transform: translateZ(60px) translateX(20px) translateY(-20px) scale(1.2); opacity: 0.8; }
          75% { opacity: 0.4; transform: translateZ(30px) translateX(10px) translateY(-10px) scale(1); }
        }
        
        @keyframes core-pulse {
          0%, 100% { transform: scale(0.8); opacity: 0.8; filter: blur(5px); }
          50% { transform: scale(1.3); opacity: 1; filter: blur(7px); }
        }
        
        @keyframes code-scroll {
          0% { transform: translateX(200px) rotateY(20deg); opacity: 0; }
          10% { opacity: 0.7; }
          90% { opacity: 0.7; }
          100% { transform: translateX(-500px) rotateY(20deg); opacity: 0; }
        }
        
        @keyframes corner-pulse {
          0% { border-color: rgba(0, 221, 255, 0.5); width: 20px; height: 20px; }
          100% { border-color: rgba(255, 0, 222, 0.5); width: 30px; height: 30px; }
        }
        
        @keyframes text-flicker {
          0%, 100% { opacity: 1; }
          8%, 10% { opacity: 0.6; }
          9% { opacity: 0.9; }
          52%, 54% { opacity: 0.6; }
          53% { opacity: 0.9; }
        }
        
        @keyframes progress {
          0% { width: 0; }
          20% { width: 20%; }
          40% { width: 38%; }
          50% { width: 52%; }
          60% { width: 65%; }
          75% { width: 82%; }
          100% { width: 100%; }
        }
      `}</style>
    </div>
  );
};

export default HologramLoader;