import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Circle, Gamepad2, Target, Zap, Trophy } from "lucide-react";

export type SportType = {
  id: string;
  name: string;
  icon: React.ReactNode;
  color: string;
  leagues: string[];
};

export const sportTypes: SportType[] = [
  {
    id: 'football',
    name: 'Football',
    icon: <Circle className="h-4 w-4" />,
    color: 'sports-football',
    leagues: ['Premier League', 'La Liga', 'Serie A', 'Bundesliga', 'Ligue 1']
  },
  {
    id: 'basketball',
    name: 'Basketball',
    icon: <Target className="h-4 w-4" />,
    color: 'sports-basketball',
    leagues: ['NBA', 'EuroLeague', 'NCAA', 'G League']
  },
  {
    id: 'baseball',
    name: 'Baseball',
    icon: <Circle className="h-4 w-4" />,
    color: 'sports-baseball',
    leagues: ['MLB', 'NPB', 'KBO', 'Minor League']
  },
  {
    id: 'tennis',
    name: 'Tennis',
    icon: <Zap className="h-4 w-4" />,
    color: 'sports-tennis',
    leagues: ['ATP', 'WTA', 'Grand Slam', 'ITF']
  },
  {
    id: 'esports',
    name: 'Esports',
    icon: <Gamepad2 className="h-4 w-4" />,
    color: 'primary',
    leagues: ['League of Legends', 'CS2', 'Dota 2', 'Valorant']
  }
];

interface SportTypeSelectorProps {
  selectedSport?: string;
  onSportChange: (sport: string) => void;
  showBadges?: boolean;
}

export const SportTypeSelector = ({ selectedSport, onSportChange, showBadges = false }: SportTypeSelectorProps) => {
  if (showBadges) {
    return (
      <div className="flex flex-wrap gap-2">
        <Badge 
          variant={!selectedSport || selectedSport === 'all' ? "default" : "secondary"}
          className="cursor-pointer"
          onClick={() => onSportChange('all')}
        >
          All Sports
        </Badge>
        {sportTypes.map((sport) => (
          <Badge
            key={sport.id}
            variant={selectedSport === sport.id ? "default" : "secondary"}
            className="cursor-pointer flex items-center gap-1"
            onClick={() => onSportChange(sport.id)}
          >
            {sport.icon}
            {sport.name}
          </Badge>
        ))}
      </div>
    );
  }

  return (
    <Select value={selectedSport || 'all'} onValueChange={onSportChange}>
      <SelectTrigger className="w-full">
        <SelectValue placeholder="Select sport type" />
      </SelectTrigger>
      <SelectContent>
        <SelectItem value="all">All Sports</SelectItem>
        {sportTypes.map((sport) => (
          <SelectItem key={sport.id} value={sport.id}>
            <div className="flex items-center gap-2">
              {sport.icon}
              {sport.name}
            </div>
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
};