import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { 
  Settings, 
  Globe, 
  Database, 
  Zap, 
  Shield,
  Save,
  RotateCcw,
  Info
} from 'lucide-react';

interface GlobalSetting {
  key: string;
  value: any;
  description: string;
  category: 'scraping' | 'prediction' | 'security' | 'general';
  type: 'string' | 'number' | 'boolean' | 'json';
}

export const GlobalSettingsManager: React.FC = () => {
  const [settings, setSettings] = useState<GlobalSetting[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);
  const { toast } = useToast();

  // Default settings structure
  const defaultSettings: GlobalSetting[] = [
    // Scraping Settings
    {
      key: 'scraping_enabled',
      value: true,
      description: 'Enable/disable automatic data scraping',
      category: 'scraping',
      type: 'boolean'
    },
    {
      key: 'scraping_interval_hours',
      value: 6,
      description: 'Hours between scraping cycles',
      category: 'scraping',
      type: 'number'
    },
    {
      key: 'max_concurrent_scrapes',
      value: 5,
      description: 'Maximum number of concurrent scraping operations',
      category: 'scraping',
      type: 'number'
    },
    {
      key: 'scraping_sources',
      value: JSON.stringify([
        'espn.com',
        'yahoo.com/sports',
        'bleacherreport.com',
        'si.com',
        'cbssports.com'
      ]),
      description: 'List of sports data sources to scrape',
      category: 'scraping',
      type: 'json'
    },
    
    // Prediction Settings
    {
      key: 'prediction_model_version',
      value: 'v2.1',
      description: 'Current ML model version for predictions',
      category: 'prediction',
      type: 'string'
    },
    {
      key: 'min_confidence_threshold',
      value: 0.65,
      description: 'Minimum confidence score for predictions',
      category: 'prediction',
      type: 'number'
    },
    {
      key: 'auto_generate_predictions',
      value: true,
      description: 'Automatically generate predictions from scraped data',
      category: 'prediction',
      type: 'boolean'
    },
    {
      key: 'prediction_leagues',
      value: JSON.stringify(['NFL', 'NBA', 'MLB', 'NHL', 'EPL', 'Champions League']),
      description: 'Leagues to generate predictions for',
      category: 'prediction',
      type: 'json'
    },

    // Security Settings
    {
      key: 'api_rate_limit_per_hour',
      value: 1000,
      description: 'API calls allowed per hour per user',
      category: 'security',
      type: 'number'
    },
    {
      key: 'require_email_verification',
      value: true,
      description: 'Require email verification for new accounts',
      category: 'security',
      type: 'boolean'
    },
    {
      key: 'admin_session_timeout_minutes',
      value: 60,
      description: 'Admin session timeout in minutes',
      category: 'security',
      type: 'number'
    },

    // General Settings
    {
      key: 'site_maintenance_mode',
      value: false,
      description: 'Enable maintenance mode for the site',
      category: 'general',
      type: 'boolean'
    },
    {
      key: 'max_free_predictions_per_day',
      value: 5,
      description: 'Maximum free predictions per user per day',
      category: 'general',
      type: 'number'
    },
    {
      key: 'support_email',
      value: '<EMAIL>',
      description: 'Support contact email',
      category: 'general',
      type: 'string'
    }
  ];

  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    setIsLoading(true);
    try {
      // Import API service
      const { apiService } = await import('@/services/api');

      // Fetch settings from backend API
      const response = await apiService.getGlobalSettings();

      if (!response.success || !response.data) {
        console.error('API error:', response.error);
        // Use defaults if API fails
        setSettings(defaultSettings);
        toast({
          title: "Warning",
          description: "Using default settings. API connection failed.",
          variant: "destructive",
        });
        return;
      }

      // Merge database settings with defaults
      const mergedSettings = defaultSettings.map(defaultSetting => {
        const dbSetting = response.data?.find((db: any) => db.key === defaultSetting.key);
        if (dbSetting) {
          return {
            ...defaultSetting,
            value: dbSetting.value
          };
        }
        return defaultSetting;
      });

      setSettings(mergedSettings);
      toast({
        title: "Settings Loaded",
        description: "Global settings loaded from database",
      });
    } catch (error) {
      console.error('Error loading settings:', error);
      setSettings(defaultSettings);
      toast({
        title: "Error",
        description: "Failed to load settings from database. Using defaults.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const updateSetting = (key: string, value: any) => {
    setSettings(prev => prev.map(setting => 
      setting.key === key ? { ...setting, value } : setting
    ));
    setHasChanges(true);
  };

  const saveSettings = async () => {
    setIsLoading(true);
    try {
      const { apiService } = await import('@/services/api');

      // Save each setting individually
      for (const setting of settings) {
        const response = await apiService.updateGlobalSetting(setting.key, setting.value);
        if (!response.success) {
          throw new Error(response.error || 'Failed to save setting');
        }
      }

      // All settings saved successfully

      toast({
        title: "Settings Saved",
        description: "Global settings updated successfully in database",
      });
      setHasChanges(false);
    } catch (error) {
      console.error('Error saving settings:', error);
      toast({
        title: "Error",
        description: "Failed to save settings to database",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const resetSettings = () => {
    setSettings(defaultSettings);
    setHasChanges(true);
    toast({
      title: "Settings Reset",
      description: "Settings reset to defaults",
    });
  };

  const renderSettingInput = (setting: GlobalSetting) => {
    switch (setting.type) {
      case 'boolean':
        return (
          <Switch
            checked={setting.value}
            onCheckedChange={(checked) => updateSetting(setting.key, checked)}
          />
        );
      
      case 'number':
        return (
          <Input
            type="number"
            value={setting.value}
            onChange={(e) => updateSetting(setting.key, Number(e.target.value))}
            className="max-w-32"
          />
        );
      
      case 'json':
        return (
          <Textarea
            value={typeof setting.value === 'string' ? setting.value : JSON.stringify(setting.value, null, 2)}
            onChange={(e) => updateSetting(setting.key, e.target.value)}
            className="font-mono text-sm"
            rows={4}
          />
        );
      
      default:
        return (
          <Input
            value={setting.value}
            onChange={(e) => updateSetting(setting.key, e.target.value)}
          />
        );
    }
  };

  const settingsByCategory = settings.reduce((acc, setting) => {
    if (!acc[setting.category]) {
      acc[setting.category] = [];
    }
    acc[setting.category].push(setting);
    return acc;
  }, {} as Record<string, GlobalSetting[]>);

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-foreground">Global Settings</h2>
          <p className="text-muted-foreground">Configure system-wide settings and parameters</p>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={resetSettings}
            disabled={isLoading}
          >
            <RotateCcw className="h-4 w-4 mr-2" />
            Reset to Defaults
          </Button>
          <Button
            onClick={saveSettings}
            disabled={isLoading || !hasChanges}
          >
            <Save className="h-4 w-4 mr-2" />
            Save Changes
          </Button>
        </div>
      </div>

      {hasChanges && (
        <Alert>
          <Info className="h-4 w-4" />
          <AlertDescription>
            You have unsaved changes. Click "Save Changes" to apply them.
          </AlertDescription>
        </Alert>
      )}

      <Tabs defaultValue="scraping" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="scraping" className="flex items-center gap-2">
            <Zap className="h-4 w-4" />
            Scraping
          </TabsTrigger>
          <TabsTrigger value="prediction" className="flex items-center gap-2">
            <Database className="h-4 w-4" />
            Prediction
          </TabsTrigger>
          <TabsTrigger value="security" className="flex items-center gap-2">
            <Shield className="h-4 w-4" />
            Security
          </TabsTrigger>
          <TabsTrigger value="general" className="flex items-center gap-2">
            <Globe className="h-4 w-4" />
            General
          </TabsTrigger>
        </TabsList>

        {Object.entries(settingsByCategory).map(([category, categorySettings]) => (
          <TabsContent key={category} value={category} className="space-y-4">
            {categorySettings.map((setting) => (
              <Card key={setting.key} className="border-border">
                <CardHeader className="pb-3">
                  <div className="flex justify-between items-start">
                    <div>
                      <CardTitle className="text-base">{setting.key.replace(/_/g, ' ').toUpperCase()}</CardTitle>
                      <CardDescription>{setting.description}</CardDescription>
                    </div>
                    <Badge variant="outline">{setting.type}</Badge>
                  </div>
                </CardHeader>
                <CardContent>
                  {renderSettingInput(setting)}
                </CardContent>
              </Card>
            ))}
          </TabsContent>
        ))}
      </Tabs>
    </div>
  );
};