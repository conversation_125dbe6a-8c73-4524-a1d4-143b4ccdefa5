import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Twitter, Facebook, Instagram, Mail, Phone, MapPin, MessageCircle } from 'lucide-react';

const Footer: React.FC = () => {
  return (
    <footer className="bg-card/95 border-t border-border mt-16">
      <div className="container mx-auto px-4 py-12">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Logo & Description */}
          <div className="md:col-span-1">
            <div className="flex items-center space-x-3 mb-4">
              <img 
                src="/lovable-uploads/d2e31152-4343-4f36-a76d-fd6714c7dc7b.png" 
                alt="1300BLK AI" 
                className="w-8 h-8 object-contain"
              />
              <span className="text-xl font-bruno tracking-wide text-foreground">1300BLK AI</span>
            </div>
            <p className="text-sm text-muted-foreground mb-4">
              Next-generation AI sports predictions with unmatched accuracy and intelligence.
            </p>
            <div className="flex space-x-3">
              <Button variant="outline" size="sm" className="p-2" asChild>
                <a href="https://twitter.com/1300blk.online" target="_blank" rel="noopener noreferrer" aria-label="Twitter">
                  <Twitter className="h-4 w-4" />
                </a>
              </Button>
              <Button variant="outline" size="sm" className="p-2" asChild>
                <a href="https://facebook.com/1300blk.online" target="_blank" rel="noopener noreferrer" aria-label="Facebook">
                  <Facebook className="h-4 w-4" />
                </a>
              </Button>
              <Button variant="outline" size="sm" className="p-2" asChild>
                <a href="https://instagram.com/1300blk.online" target="_blank" rel="noopener noreferrer" aria-label="Instagram">
                  <Instagram className="h-4 w-4" />
                </a>
              </Button>
              <Button variant="outline" size="sm" className="p-2" asChild>
                <a href="https://discord.gg/vd6G5fuv" target="_blank" rel="noopener noreferrer" aria-label="Discord">
                  <MessageCircle className="h-4 w-4" />
                </a>
              </Button>
              <Button variant="outline" size="sm" className="p-2" asChild>
                <a href="https://t.me/+ThiLn16PDrsxYmFk" target="_blank" rel="noopener noreferrer" aria-label="Telegram">
                  <svg className="h-4 w-4" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M12 0C5.373 0 0 5.373 0 12s5.373 12 12 12 12-5.373 12-12S18.627 0 12 0zm5.562 8.161c-.18 1.897-.962 6.502-1.359 8.627-.168.9-.5 1.201-.82 1.23-.697.064-1.226-.461-1.901-.903-1.056-.692-1.653-1.123-2.678-1.799-1.185-.781-.417-1.21.258-1.911.177-.184 3.247-2.977 3.307-3.23.007-.032.014-.15-.056-.212s-.174-.041-.249-.024c-.106.024-1.793 1.14-5.061 3.345-.479.33-.913.49-1.302.48-.428-.008-1.252-.241-1.865-.44-.752-.244-1.349-.374-1.297-.789.027-.216.325-.437.893-.663 3.498-1.524 5.831-2.529 6.998-3.014 3.332-1.386 4.025-1.627 4.476-1.635z"/>
                  </svg>
                </a>
              </Button>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h4 className="font-semibold text-foreground mb-4">Quick Links</h4>
            <div className="space-y-2">
              <Link to="/predictions" className="block text-sm text-muted-foreground hover:text-primary transition-colors">
                Predictions
              </Link>
              <Link to="/leagues" className="block text-sm text-muted-foreground hover:text-primary transition-colors">
                Leagues
              </Link>
              <Link to="/tips" className="block text-sm text-muted-foreground hover:text-primary transition-colors">
                Expert Tips
              </Link>
              <Link to="/auth/signup" className="block text-sm text-muted-foreground hover:text-primary transition-colors">
                Join Elite
              </Link>
            </div>
          </div>

          {/* Support */}
          <div>
            <h4 className="font-semibold text-foreground mb-4">Support</h4>
            <div className="space-y-2">
              <Link to="/help" className="block text-sm text-muted-foreground hover:text-primary transition-colors">
                Help Center
              </Link>
              <Link to="/contact" className="block text-sm text-muted-foreground hover:text-primary transition-colors">
                Contact Us
              </Link>
              <Link to="/privacy" className="block text-sm text-muted-foreground hover:text-primary transition-colors">
                Privacy Policy
              </Link>
              <Link to="/terms" className="block text-sm text-muted-foreground hover:text-primary transition-colors">
                Terms of Service
              </Link>
            </div>
          </div>

          {/* Contact Info */}
          <div>
            <h4 className="font-semibold text-foreground mb-4">Contact</h4>
            <div className="space-y-3">
              <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                <Mail className="h-4 w-4" />
                <span><EMAIL></span>
              </div>
              <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                <Phone className="h-4 w-4" />
                <span>+****************</span>
              </div>
              <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                <MapPin className="h-4 w-4" />
                <span>Los Angeles, CA</span>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-border mt-8 pt-8 flex flex-col md:flex-row justify-between items-center">
          <p className="text-sm text-muted-foreground">
            © 2024 1300BLK AI. All rights reserved.
          </p>
          <p className="text-sm text-muted-foreground mt-2 md:mt-0">
            Powered by Advanced AI Intelligence
          </p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;