import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useToast } from '@/hooks/use-toast';
import { 
  Play, 
  Pause, 
  RefreshCw, 
  Database, 
  Globe,
  CheckCircle,
  XCircle,
  Clock,
  Target,
  TrendingUp,
  Zap
} from 'lucide-react';

interface ScrapingSource {
  id: string;
  name: string;
  url: string;
  status: 'active' | 'inactive' | 'error';
  lastScrape: string;
  totalRecords: number;
  successRate: number;
}

interface ScrapingJob {
  id: string;
  source: string;
  status: 'running' | 'completed' | 'failed' | 'pending';
  progress: number;
  recordsFound: number;
  startTime: string;
  endTime?: string;
  error?: string;
}

export const DataScrapingManager: React.FC = () => {
  const [sources, setSources] = useState<ScrapingSource[]>([]);
  const [jobs, setJobs] = useState<ScrapingJob[]>([]);
  const [isScrapingActive, setIsScrapingActive] = useState(false);
  const [newSourceUrl, setNewSourceUrl] = useState('');
  const { toast } = useToast();

  // Mock data for demonstration
  const mockSources: ScrapingSource[] = [
    {
      id: '1',
      name: 'ESPN Sports Data',
      url: 'espn.com',
      status: 'active',
      lastScrape: '2024-01-15T10:30:00Z',
      totalRecords: 15420,
      successRate: 98.5
    },
    {
      id: '2',
      name: 'Yahoo Sports',
      url: 'yahoo.com/sports',
      status: 'active',
      lastScrape: '2024-01-15T10:25:00Z',
      totalRecords: 12340,
      successRate: 96.2
    },
    {
      id: '3',
      name: 'Bleacher Report',
      url: 'bleacherreport.com',
      status: 'error',
      lastScrape: '2024-01-15T09:45:00Z',
      totalRecords: 8920,
      successRate: 89.1
    },
    {
      id: '4',
      name: 'Sports Illustrated',
      url: 'si.com',
      status: 'active',
      lastScrape: '2024-01-15T10:35:00Z',
      totalRecords: 9876,
      successRate: 94.7
    },
    {
      id: '5',
      name: 'CBS Sports',
      url: 'cbssports.com',
      status: 'active',
      lastScrape: '2024-01-15T10:20:00Z',
      totalRecords: 11234,
      successRate: 97.3
    }
  ];

  const mockJobs: ScrapingJob[] = [
    {
      id: '1',
      source: 'ESPN Sports Data',
      status: 'completed',
      progress: 100,
      recordsFound: 342,
      startTime: '2024-01-15T10:30:00Z',
      endTime: '2024-01-15T10:35:00Z'
    },
    {
      id: '2',
      source: 'Yahoo Sports',
      status: 'running',
      progress: 67,
      recordsFound: 189,
      startTime: '2024-01-15T10:25:00Z'
    },
    {
      id: '3',
      source: 'Sports Illustrated',
      status: 'pending',
      progress: 0,
      recordsFound: 0,
      startTime: '2024-01-15T10:40:00Z'
    }
  ];

  useEffect(() => {
    setSources(mockSources);
    setJobs(mockJobs);
    setIsScrapingActive(true);
  }, []);

  const startGlobalScraping = async () => {
    setIsScrapingActive(true);
    toast({
      title: "Scraping Started",
      description: "Global data scraping has been initiated across all sources",
    });

    // Simulate scraping progress
    const newJobs = sources.map((source, index) => ({
      id: `job_${Date.now()}_${index}`,
      source: source.name,
      status: 'running' as const,
      progress: 0,
      recordsFound: 0,
      startTime: new Date().toISOString()
    }));

    setJobs(newJobs);

    // Simulate progress updates
    newJobs.forEach((job, index) => {
      const progressInterval = setInterval(() => {
        setJobs(current => current.map(j => {
          if (j.id === job.id) {
            const newProgress = Math.min(j.progress + Math.random() * 15, 100);
            const recordsFound = Math.floor(newProgress * 5);
            
            if (newProgress >= 100) {
              clearInterval(progressInterval);
              return {
                ...j,
                status: 'completed' as const,
                progress: 100,
                recordsFound,
                endTime: new Date().toISOString()
              };
            }
            
            return { ...j, progress: newProgress, recordsFound };
          }
          return j;
        }));
      }, 1000 + index * 200);
    });
  };

  const stopGlobalScraping = () => {
    setIsScrapingActive(false);
    setJobs(current => current.map(job => ({
      ...job,
      status: job.status === 'running' ? 'failed' as const : job.status,
      error: job.status === 'running' ? 'Manually stopped' : job.error
    })));

    toast({
      title: "Scraping Stopped",
      description: "All scraping operations have been halted",
      variant: "destructive",
    });
  };

  const addNewSource = () => {
    if (!newSourceUrl) return;

    const newSource: ScrapingSource = {
      id: Date.now().toString(),
      name: newSourceUrl.replace(/^https?:\/\//, '').replace(/\/.*/, ''),
      url: newSourceUrl,
      status: 'inactive',
      lastScrape: new Date().toISOString(),
      totalRecords: 0,
      successRate: 0
    };

    setSources(current => [...current, newSource]);
    setNewSourceUrl('');
    
    toast({
      title: "Source Added",
      description: `${newSource.name} has been added to scraping sources`,
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
      case 'completed':
        return 'bg-green-500';
      case 'running':
        return 'bg-blue-500';
      case 'error':
      case 'failed':
        return 'bg-red-500';
      case 'inactive':
      case 'pending':
        return 'bg-gray-500';
      default:
        return 'bg-gray-500';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
      case 'completed':
        return <CheckCircle className="h-4 w-4" />;
      case 'running':
        return <RefreshCw className="h-4 w-4 animate-spin" />;
      case 'error':
      case 'failed':
        return <XCircle className="h-4 w-4" />;
      case 'pending':
        return <Clock className="h-4 w-4" />;
      default:
        return <Globe className="h-4 w-4" />;
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-foreground flex items-center gap-2">
            <Database className="h-6 w-6" />
            Data Scraping Manager
          </h2>
          <p className="text-muted-foreground">Manage and monitor sports data scraping across all sources</p>
        </div>
        <div className="flex gap-2">
          {isScrapingActive ? (
            <Button variant="destructive" onClick={stopGlobalScraping}>
              <Pause className="h-4 w-4 mr-2" />
              Stop All Scraping
            </Button>
          ) : (
            <Button onClick={startGlobalScraping}>
              <Play className="h-4 w-4 mr-2" />
              Start Global Scraping
            </Button>
          )}
        </div>
      </div>

      {/* Status Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Active Sources</p>
                <p className="text-2xl font-bold text-primary">
                  {sources.filter(s => s.status === 'active').length}
                </p>
              </div>
              <Globe className="h-8 w-8 text-primary" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Running Jobs</p>
                <p className="text-2xl font-bold text-blue-500">
                  {jobs.filter(j => j.status === 'running').length}
                </p>
              </div>
              <RefreshCw className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Total Records</p>
                <p className="text-2xl font-bold text-green-500">
                  {sources.reduce((sum, s) => sum + s.totalRecords, 0).toLocaleString()}
                </p>
              </div>
              <Target className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Avg Success Rate</p>
                <p className="text-2xl font-bold text-purple-500">
                  {(sources.reduce((sum, s) => sum + s.successRate, 0) / sources.length).toFixed(1)}%
                </p>
              </div>
              <TrendingUp className="h-8 w-8 text-purple-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="sources" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="sources">Data Sources</TabsTrigger>
          <TabsTrigger value="jobs">Active Jobs</TabsTrigger>
          <TabsTrigger value="add-source">Add New Source</TabsTrigger>
        </TabsList>

        <TabsContent value="sources" className="space-y-4">
          <div className="grid gap-4">
            {sources.map((source) => (
              <Card key={source.id} className="border-border">
                <CardHeader className="pb-3">
                  <div className="flex justify-between items-start">
                    <div>
                      <CardTitle className="text-lg">{source.name}</CardTitle>
                      <CardDescription className="flex items-center gap-2">
                        <Globe className="h-4 w-4" />
                        {source.url}
                      </CardDescription>
                    </div>
                    <Badge 
                      variant="outline" 
                      className={`${getStatusColor(source.status)} text-white border-0`}
                    >
                      {getStatusIcon(source.status)}
                      <span className="ml-1 capitalize">{source.status}</span>
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-3 gap-4 text-sm">
                    <div>
                      <p className="text-muted-foreground">Last Scrape</p>
                      <p className="font-medium">
                        {new Date(source.lastScrape).toLocaleString()}
                      </p>
                    </div>
                    <div>
                      <p className="text-muted-foreground">Total Records</p>
                      <p className="font-medium">{source.totalRecords.toLocaleString()}</p>
                    </div>
                    <div>
                      <p className="text-muted-foreground">Success Rate</p>
                      <p className="font-medium">{source.successRate}%</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="jobs" className="space-y-4">
          {jobs.length === 0 ? (
            <Alert>
              <Zap className="h-4 w-4" />
              <AlertDescription>
                No active scraping jobs. Start global scraping to see job progress here.
              </AlertDescription>
            </Alert>
          ) : (
            <div className="space-y-4">
              {jobs.map((job) => (
                <Card key={job.id} className="border-border">
                  <CardHeader className="pb-3">
                    <div className="flex justify-between items-start">
                      <div>
                        <CardTitle className="text-lg">{job.source}</CardTitle>
                        <CardDescription>
                          Started: {new Date(job.startTime).toLocaleString()}
                        </CardDescription>
                      </div>
                      <Badge 
                        variant="outline" 
                        className={`${getStatusColor(job.status)} text-white border-0`}
                      >
                        {getStatusIcon(job.status)}
                        <span className="ml-1 capitalize">{job.status}</span>
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="flex justify-between text-sm">
                      <span>Progress: {job.progress.toFixed(1)}%</span>
                      <span>Records Found: {job.recordsFound}</span>
                    </div>
                    <Progress value={job.progress} className="w-full" />
                    {job.error && (
                      <Alert variant="destructive">
                        <XCircle className="h-4 w-4" />
                        <AlertDescription>{job.error}</AlertDescription>
                      </Alert>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>

        <TabsContent value="add-source" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Add New Data Source</CardTitle>
              <CardDescription>
                Add a new website to scrape for sports data and predictions
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex gap-2">
                <Input
                  placeholder="https://example.com"
                  value={newSourceUrl}
                  onChange={(e) => setNewSourceUrl(e.target.value)}
                />
                <Button onClick={addNewSource} disabled={!newSourceUrl}>
                  Add Source
                </Button>
              </div>
              
              <Alert>
                <Globe className="h-4 w-4" />
                <AlertDescription>
                  Make sure the website allows scraping and contains sports data relevant to predictions.
                </AlertDescription>
              </Alert>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};