import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Starting database seeding...');

  // Create admin user
  const hashedPassword = await bcrypt.hash('admin123', 12);

  const adminUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      password: hashedPassword,
      fullName: 'Admin User',
      role: 'SUPER_ADMIN',
      subscriptionTier: 'ENTERPRISE',
      emailVerified: true,
    },
  });

  console.log('✅ Created admin user:', adminUser.email);

  // Create test user
  const testUserPassword = await bcrypt.hash('test123', 12);
  
  const testUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      password: testUserPassword,
      fullName: 'Test User',
      role: 'USER',
      subscriptionTier: 'FREE',
      emailVerified: true,
    },
  });

  console.log('✅ Created test user:', testUser.email);

  // Create global settings
  await prisma.globalSettings.upsert({
    where: { key: 'prediction_confidence_threshold' },
    update: {},
    create: {
      key: 'prediction_confidence_threshold',
      value: '0.65',
    },
  });

  await prisma.globalSettings.upsert({
    where: { key: 'scraping_enabled' },
    update: {},
    create: {
      key: 'scraping_enabled',
      value: 'true',
    },
  });

  await prisma.globalSettings.upsert({
    where: { key: 'supported_sports' },
    update: {},
    create: {
      key: 'supported_sports',
      value: JSON.stringify(['football', 'basketball', 'baseball', 'hockey', 'soccer']),
    },
  });

  console.log('✅ Created global settings');

  // Create sample sports data
  const sampleMatches = [
    {
      sport: 'football',
      league: 'NFL',
      homeTeam: 'Kansas City Chiefs',
      awayTeam: 'Baltimore Ravens',
      matchDate: new Date('2024-09-05T20:20:00Z'),
      status: 'UPCOMING',
      odds: JSON.stringify({
        moneyline: { home: -110, away: +105 },
        spread: { home: -2.5, away: +2.5 },
        total: { over: 47.5, under: 47.5 }
      }),
      stats: JSON.stringify({
        home_rating: 88.5,
        away_rating: 85.2,
        home_offense_rating: 92.1,
        away_offense_rating: 87.8,
        home_defense_rating: 84.9,
        away_defense_rating: 82.6
      }),
      source: 'ESPN',
      externalId: 'nfl_2024_week1_chiefs_ravens',
    },
    {
      sport: 'basketball',
      league: 'NBA',
      homeTeam: 'Los Angeles Lakers',
      awayTeam: 'Golden State Warriors',
      matchDate: new Date('2024-09-06T22:00:00Z'),
      status: 'UPCOMING',
      odds: JSON.stringify({
        moneyline: { home: +120, away: -140 },
        spread: { home: +3.5, away: -3.5 },
        total: { over: 225.5, under: 225.5 }
      }),
      stats: JSON.stringify({
        home_rating: 82.3,
        away_rating: 86.7,
        home_offense_rating: 85.1,
        away_offense_rating: 89.4,
        home_defense_rating: 79.5,
        away_defense_rating: 84.0
      }),
      source: 'ESPN',
      externalId: 'nba_2024_lakers_warriors',
    },
  ];

  for (const match of sampleMatches) {
    await prisma.sportsData.create({
      data: match,
    });
  }

  console.log('✅ Created sample sports data');

  // Create sample predictions
  const samplePredictions = [
    {
      sport: 'football',
      league: 'NFL',
      homeTeam: 'Kansas City Chiefs',
      awayTeam: 'Baltimore Ravens',
      matchDate: new Date('2024-09-05T20:20:00Z'),
      market: 'Moneyline',
      pick: 'Kansas City Chiefs',
      confidence: 72.5,
      odds: -110,
      expectedValue: 0.15,
      factors: JSON.stringify({
        home_field_advantage: 0.15,
        recent_form: 0.12,
        head_to_head: 0.08,
        injury_impact: -0.05,
        weather_conditions: 0.02
      }),
      analysis: 'Chiefs have strong home field advantage and better recent form. Ravens missing key defensive player.',
      source: 'ML',
      status: 'UPCOMING',
      isPremium: true,
      userId: adminUser.id,
    },
    {
      sport: 'basketball',
      league: 'NBA',
      homeTeam: 'Los Angeles Lakers',
      awayTeam: 'Golden State Warriors',
      matchDate: new Date('2024-09-06T22:00:00Z'),
      market: 'Spread',
      pick: 'Golden State Warriors -3.5',
      confidence: 68.2,
      odds: -110,
      expectedValue: 0.12,
      factors: JSON.stringify({
        offensive_efficiency: 0.18,
        defensive_rating: 0.10,
        pace_advantage: 0.08,
        rest_advantage: 0.05,
        travel_fatigue: -0.03
      }),
      analysis: 'Warriors superior offensive efficiency and pace should cover the spread against Lakers.',
      source: 'ML',
      status: 'UPCOMING',
      isPremium: false,
      userId: adminUser.id,
    },
  ];

  for (const prediction of samplePredictions) {
    await prisma.prediction.create({
      data: prediction,
    });
  }

  console.log('✅ Created sample predictions');

  console.log('🎉 Database seeding completed successfully!');
}

main()
  .catch((e) => {
    console.error('❌ Error during seeding:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
